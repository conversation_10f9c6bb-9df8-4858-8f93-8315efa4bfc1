/*
 =================================================
  ZineOne App styles
  Copyright (c) 2013 ZineOne Inc.
  All rights reserved 
 =================================================
 */

/*
====================================
Override Dojo
====================================
*/

/*
====================================
Common Styles
====================================
*/

/*
====================================
Devices - layout specific CSS
====================================
*/
@
viewport {
	zoom: 1;
	width: device-width;
}

/* Generic small screen criteria. */
@media screen and (max-width:979px) {
}

.c3_cj_outer_body {
	-webkit-flex: 1;
	flex: 1;
	/* overflow here and in body_c2 - causes Seg Step box tooltip fail */
	/* move tooltip position to center so it does not overflow container div */
	/*overflow: hidden; */
	overflow-x: hidden;
	overflow-y: hidden;
}

.c3_cj_outer_body_c1 {
	width: 200px;
	/* background-color: azure; */
	background-color: #fff;
	border-right: 1px solid rgba(228, 233, 233, 0.91);
	padding: 2px 6px;
	font-family: 'Open Sans', sans-serif !important;
}

.c3_cj_outer_body_c1 .header {
}

.c3_cj_outer_body_c1 .section {
	font-size: 14px;
	color: rgb(124, 177, 228);
	padding-bottom: 5px;
}

.c3_cj_outer_body_c1 .text, .c3_cj_outer_body_c1 .smallText,
	.c3_cj_outer_body_c1 .num {
}

.c3_cj_outer_body_c2 {
	-webkit-flex: 1;
	flex: 1;
	position: relative;
}

.c3_cj_overview {
	border-right: 1px solid rgba(228, 233, 233, 0.91);
}

.c3_cj_overview .header {
	font-size: 16px;
	color: green;
	padding-bottom: 24px;
}

.c3_cj_overview .section {
	font-weight: bolder;
	color: #bbb;
	font-family: sans-serif;
	font-size: 12px;
}

.c3_cj_overview .text {
	color: #555;
	padding-bottom: 15px;
	overflow: hidden;
	text-overflow: ellipsis;
}

.c3_cj_overview .num {
	font-size: 20px;
	background: #fff;
	padding-bottom: 0px;
	color: gray;
}

.c3_cj_overview .smallText {
	font-size: 10px;
	padding-bottom: 20px;
}

.c3_cj_overview .helpText {
	font-size: 12px;
	padding-top: 40px;
	color: #aaa;
}

.c3_cpgn_ov_imgdv {
	
}

.c3_cpgn_ov_imgdv.c3_cols_nowrap {
	-webkit-justify-content: flex-end;
	justify-content: flex-end;
	-webkit-flex: 1 0 auto;
	flex: 1 0 auto;
}

.c3_cj_videotutorial_box {
	margin-bottom: 2px;
	overflow: hidden;
}

.c3_cj_videotutorial_box h2 {
	font-size: 13px;
	line-height: 22px;
	color: #0055A9;
	padding: 0;
	margin: 5px;
	font-weight: 300;
	letter-spacing: 1px;
}

.c3_cpgn_ov_imgdv_vdo {
	position: relative;
}

.c3_cpgn_ov_img {
	width: 100%;
}

.c3_vdo_play_btn {
	position: absolute;
	right: 14px;
	bottom: 14px;
	color: #3e72c8;
	border-radius: 50%;
	border-radius: 6px;
	width: 24px;
	height: 24px;
	border: 2px solid #3e72c8;
	background-color: rgba(245, 245, 245, 0.45);
	cursor: pointer;
}

.c3_cj_videotutorial_box .c3_video_box {
	/*border: 3px solid #e1e1e1;*/
	overflow: hidden;
}

/**
Css for create journey and select journey steps
**/
.journey_body {
	background: aliceBlue;
	-webkit-flex: 0 0 auto;
	flex: 0 0 auto;
	min-height: 111px;
}

.journey_add_btn {
	cursor: pointer;
	border-radius: 3px;
	background-color: #fff;
	-webkit-align-self: center;
	align-self: center;
}

.journey_step_item {
	width: 100%;
	height: 30px;
	padding: 5px;
	font-size: 14px;
	color: #777777;
	border-bottom: 1px solid #d9d9d9;
	cursor: pointer;
}

div.journey_step_item:hover {
	background-color: #e6e6e6;
}

.journey_float_left {
	float: left;
}

.journey_step_img {
	width: 15px;
}

.journey_step_name {
	padding: 5px 5px 10px 10px;
	margin-top: 10px;
	font-size: 14px;
	color: rgb(0, 85, 169);
	text-align: center;
	cursor: pointer;
}

.journey_step_name .c3_jorney_title_ellipsis {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	width: 110px;
	margin: 0 auto;
}

.journey_step_users {
	font-size: 12px;
	padding: 2px 3px 10px 3px;
	text-align: center;
}

.journey_step_del {
	background: #d9d9d9;
	background: #9FC2D2;
	padding: 2px 5px;
	text-align: center;
	color: #fff;
	font-size: 12px;
	cursor: pointer;
}

.journey_step_del:hover {
	background: #74A8C0;
}

.journey_step_btn i {
	font-size: 11px;
}

.c3_cj_timeline_wrp {
	-webkit-flex: 1 0 0;
	flex: 1 0 0;
	overflow-y: auto;
	overflow-y: hidden;
	padding: 5px 15px;
	border: 1px solid;
	/*border-left: 30px solid;*/
	border-color: rgba(95, 158, 160, 0.27);
	border-top-color: rgba(220, 220, 220, 0.47);
}

.c3_cj_timeline_wrp>div {
	-webkit-flex: 1 0 0;
	flex: 1 0 0;
	overflow-y: hidden;
}

.c3_cj_v_cont {
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_v_cont_body {
	-webkit-flex: 1;
	flex: 1;
	overflow-y: auto;
}

.c3_cj_v_head {
	height: 40px;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-justify-content: space-between;
	justify-content: space-between;
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_v_cont_body_r {
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_timeline_cont {
	-webkit-flex: 1;
	flex: 1;
	margin-bottom: 10px;
	background: #f9f9f9;
	border-radius: 5px;
	-webkit-box-shadow: 0px 0px 2px 2px rgba(229,230,227,0.5);
	-moz-box-shadow: 0px 0px 2px 2px rgba(229,230,227,0.5);
	box-shadow: 0px 0px 2px 2px rgba(229,230,227,0.5); 
}

.c3_cj_item_head {
    background: #eaeaea;
    padding: 4px;
}
.c3_cj_v_timeline {
	/*display: -webkit-flex;
	display: flex;*/
	
}

.c3_cj_item_cont_body {
	-webkit-flex: 1;
	flex: 1;
	display: -webkit-flex;
	display: flex;
	padding: 10px;
}

.c3_cj_item_timeline {
	-webkit-flex: 1;
	flex: 1;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-justify-content: flex-start;
	justify-content: flex-start;
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_item_activities {
	-webkit-flex: 1;
	flex: 1;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-justify-content: space-around;
	justify-content: space-around;
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_timeline_r2 {
	align-self: stretch;
}

.c3_cj_timeline_line {
	-webkit-flex: 1 0 auto;
	flex: 1 0 auto;
	background-color: rgba(100, 149, 237, 0.21);
	height: 10px;
}

.c3_cj_timeline_r2 .c3_cj_timeline_line {
	-webkit-align-self: center;
	align-self: center;
}

.c3_cj_timeline_obj {
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: column nowrap;
	flex-flow: column nowrap;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_timeline_arrow {
	font-size: 24px;
	color: rgba(100, 149, 237, 0.21);
}

.c3_cj_timeline_circle {
	font-size: 18px;
	font-weight: normal;
	width: 48px;
	height: 48px;
	border-radius: 48px;
	border: 1px solid #f5f5f5;
	background-color: lightblue;
	display: flex;
	-webkit-flex-flow: row;
	flex-flow: row;
	-webkit-justify-content: center;
	justify-content: center;
	-webkit-align-items: center;
	align-items: center;
	border: 3px solid #fff;
	-webkit-box-shadow: 0px 0px 2px 2px rgba(229, 230, 227, 1);
	-moz-box-shadow: 0px 0px 2px 2px rgba(229, 230, 227, 1);
	box-shadow: 0px 0px 2px 2px rgba(229, 230, 227, 1);
}

.c3_cj_timeline_point_on .c3_cj_timeline_circle {
	background-color: rgba(0, 206, 209, 0.32);
}

.c3_cj_v_activity .c3_cj_timeline_circle {
	background-color: thistle;
}

.c3_cj_timeline_circle_btn {
	cursor: pointer;
}

.c3_cj_timeline_circle_btn:hover {
	background-color: darkgrey;
	color: white;
}

.c3_cj_edit_dlg_box {
	
}

div.c3_cj_edit_dlg_box {
	top: 100px !important;
	left: 200px !important;
	/* max-width: 1200px !important; */
	right: 200px;
	bottom: 100px;
}

.c3_cj_edit_dlg_box .dijitDialogTitleBar {
	display: none;
}

.c3_cj_edit_dlg_box .dijitDialogPaneContent {
	-webkit-flex: 1 0 auto;
	flex: 1 0 auto;
	-webkit-flex: 1;
	flex: 1;
	display: -webkit-flex;
	display: flex;
	max-height: -webkit-calc(100% - 2px);
	max-height: calc(100% - 2px);
}

.c3_cj_edit_dlg_cont {
	-webkit-flex: 1 0 0;
	flex: 1 0 0;
	/* background-color: lavender; */
}

.c3_cj_edit_dlg_cont_body {
	-webkit-flex: 1;
	flex: 1;
	overflow-y: auto;
	overflow-x: hidden;
	padding-right: 2px;
}

.c3_cj_edit_dlg_head, .c3_solnTemplEvtMap_head {
	-webkit-flex: 0 0 auto;
	flex: 0 0 auto;
	border-bottom: 1px solid #ebebeb;
}

.c3_cj_e_section_foot {
	webkit-flex: 0 0 auto;
	flex: 0 0 auto;
}

.c3_cj_edit_dlg_head_col2 {
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-align-items: center;
	align-items: center;
	-webkit-justify-content: flex-end;
	justify-content: flex-end;
}


.c3_cj_edit_dlg_title {
}

.c3_cj_edit_dlg_title_middle {
	font-size: 14px;
	padding: 6px 0px;
	font-weight: lighter;
	color: #999;
}

.c3_cj_e_section {
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_e_s1_r2 {
	/* -webkit-flex: 1; */
	/* background-color: aliceblue; */
	/* flex: 1; */
}

section.c3_cj_e_section_1 {
	/* -webkit-flex: 0 0 auto; */
	/* flex: 0 0 auto; */
}

.c3_cj_edit_action_item {
	/* -webkit-flex: 1 0 auto; */
	/* flex: 1 0 auto; */
	/*background-color: ghostwhite;*/
	margin: 2px;
	/* align-self: flex-start; */
	/* max-width: 120px; */
	float: left;
	margin-right: 6px;
	cursor: pointer;
	border: 1px solid rgba(242, 239, 239, 0.8);
}

.c3_cj_edit_action_item .c3_palette_box_item_title {
	font-size: 12px;
	margin-top: 5px;
}

.c3_cj_edit_action_item:hover {
	background-color: rgb(236, 243, 252);
}

.c3_cj_edit_action_item_sel {
	background-color: rgb(236, 243, 252);
	background: #ecf3fc url('/res/green_tick.png') no-repeat left top;
}

div.c3_cj_e_s1_r2r2 {
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_e_s1_axn_time {
	-webkit-align-items: center;
	align-items: center;
	margin-right: 10px;
}

.c3_cj_e_s2_r1 {
	-webkit-flex: 0 0 auto;
	flex: 0 0 auto;
}

.c3_cj_e_s2_r2 {
	-webkit-flex: 1 0 auto;
	flex: 1 0 auto;
}

.c3_cj_e_s2_r2r2 {
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_e_s2_r2r2>div {
	-webkit-flex: 1;
	flex: 1;
	/* background-color: aquamarine; */
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: column nowrap;
	flex-flow: column nowrap;
}

.c3_cj_e_s2_r2r2 .c3_actionf_item_box {
	-webkit-flex: 1;
	flex: 1;
	/*background-color: aliceblue;*/
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: column nowrap;
	flex-flow: column nowrap;
}

.c3_cj_e_s2_r2r2 .msg_left {
	-webkit-flex: 1;
	flex: 1;
	/* background-color: antiquewhite; */
}

.c3_cj_e_s2_r2r2 .c3_actionf_mafa .msg_left {
	-webkit-flex: 0 0 auto;
	flex: 0 0 auto;
}

.c3_cj_e_s2_r2r2 div.c3_lifecycle_body_cont .c3_whitebox_channelsettings_non
	{
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_e_s3_r1 {
	-webkit-flex: 1 0 auto;
	flex: 1 0 auto;
	overflow-y: auto;
	height: 200px;
	/* background-color: aquamarine; */
}

.c3_cj_e_s3_r2 {
	-webkit-flex: 0 0 auto;
	flex: 0 0 auto;
	/*	width:170px;
	align-items:center;*/
	margin: 0 auto;
}

.c3_cj_timeline_r3 {
	
}

.c3_cj_timeline_r3_c {
	margin-right: 2px;
}

/**
Journey Summary Step
**/
.c3_cj_item_container {
	width: 325px;
	/** ZMOB-15279 temp hide Actions and Converted tabs */
	/** changed height 190px to 100px */
	height: 100px;
	margin: 0 0 2.7% 2.7%;
	position: relative;
}

.c3_cj_summary_outerDiv {
	background: #fff;
	/*width: 325px;*/
	/** ZMOB-15279 temp hide Actions and Converted tabs */
	/** changed height 190px to auto */
	/* height: 190px;*/
	height: auto;

	/* margin: 14px 0px 10px 0px; */
	position: absolute;
	left: 0px;
	right: 0px;
	-webkit-box-shadow: 0px 5px 10px 0px rgba(50, 50, 50, 0.17);
	-moz-box-shadow: 0px 5px 10px 0px rgba(50, 50, 50, 0.17);
	box-shadow: 0px 5px 10px 0px rgba(50, 50, 50, 0.17);
	border: 1px solid #E9E9E9;
}

.c3_cj_item_container .badges-incomp, .c3_cj_item_container .badges-suspended
	{
	top: 0;
}

.c3_cj_item_actions {
	position: absolute;
	top: 35%;
	/*text-align: center !important;*/
	/* height: 25px; */
	left: 27%;
	/* margin-left: 96px; */
	/* margin-top: 20px; */
	z-index: 9;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}

.c3_cj_item_visibility {
	display: none;
}

.c3_cj_summary_description {
	font-size: 12px;
	color: #767676;
	word-wrap: break-word;
	padding-top: 6px;
	font-weight: 400;
	padding-bottom: 2px;
	cursor: default;
}

.c3_cj_infoDiv {
	height: 62px;
	padding: 16px 14px 15px 14px;
}

.c3_cj_titleclearbox {
	clear: both;
	overflow: hidden;
}

.c3_cj_titleclearbox .title_name {
	float: left;
	margin-right: 10px;
	width: auto;
	padding: 3px 0;
	width: 105px;
	font-size: 16px;
	color: #3e72c8;
}

.c3_cj_titleclearbox .title_new_rename {
	background: url(/res/icon_pencil_edit.png) no-repeat 99% 6px;
	/*float: left;
	padding: 3px 15px 3px 5px;
	width: 70%;
	font-size: 82%;
	color: rgb(124, 168, 217);*/
	float: left;
	padding: 4px 15px 4px 5px;
	width: 60%;
	font-size: 85%;
	color: rgb(113, 151, 169);
	border: 1px solid #E5E5E5;
	border-radius: 0px;
	overflow: hidden;
	text-overflow: ellipsis;
}

.c3_cj_titleclearbox .title_new_rename:hover {
	background: #f9f9f9 url(/res/icon_pencil_edit.png) no-repeat 99% 6px;
}

.c3_campaign.c3_read_only .title_new_rename {
	background: initial;
}

.c3_cj_no_margin {
	margin: 0px !important;
}

.c3_cj_box1 {
	position: relative;
	/* border-radius: 3px; */
	/* border: 1px solid #d2e4ff; */
	background-color: #fff;
	margin: 10px 5px 0 5px;
	width: 260px;
	-webkit-box-shadow: 0px 0px 5px 0px rgba(204, 206, 219, 1);
	-moz-box-shadow: 0px 0px 5px 0px rgba(204, 206, 219, 1);
	box-shadow: 0px 0px 5px 0px rgba(204, 206, 219, 1);
}
.c3_cj_stepBox .c3_cj_box1 {
	min-height: 60px;
}

.c3_cj_seg_step_box {
	position: relative;
	margin-right: 0px;
}

.c3_goals_goalTitle:hover {
	background: #F9F9F9;
	cursor: pointer;
}

/* Add this attribute to the element that needs a tooltip */
.c3_ttip [data-tooltip] {
	position: relative;
	z-index: 2;
	cursor: pointer;
}

/* Hide the tooltip content by default */
.c3_ttip [data-tooltip]:before, .c3_ttip [data-tooltip]:after {
	visibility: hidden;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
	opacity: 0;
	pointer-events: none;
}

/* Position tooltip above the element */
.c3_ttip [data-tooltip]:before {
	position: absolute;
	bottom: 140%;
	left: 50%;
	margin-bottom: 5px;
	margin-left: -80px;
	padding: 4px;
	min-width: 150px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	background-color: #000;
	background-color: hsla(0, 0%, 20%, 0.9);
	color: #fff;
	content: attr(data-tooltip);
	text-align: center;
	font-size: 12px;
	line-height: 1.2;
}

/* Triangle hack to make tooltip look like a speech bubble */
.c3_ttip [data-tooltip]:after {
	position: absolute;
	bottom: 135%;
	left: 50%;
	margin-left: -5px;
	width: 0;
	border-top: 9px solid #000;
	border-top: 9px solid hsla(0, 0%, 20%, 0.9);
	border-right: 9px solid transparent;
	border-left: 9px solid transparent;
	content: " ";
	font-size: 0;
	line-height: 0;
}

/* Show tooltip content on hover */
.c3_ttip [data-tooltip]:hover:before, .c3_ttip [data-tooltip]:hover:after
	{
	visibility: visible;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
	opacity: 1;
}

/* overwrite default tip position */
.c3_cj_states_end_box[data-tooltip]:before {
	bottom: 110%;
	bottom: -40px;
}

.c3_cj_states_end_box[data-tooltip]:after {
	bottom: 106%;
	bottom: -14px;
	border-bottom: 9px solid #000;
	border-bottom: 9px solid hsla(0, 0%, 20%, 0.9);
	border-top: initial;
}

.journey_step_name[data-tooltip]:before {
	bottom: -310%;
}

.journey_step_name[data-tooltip]:after {
	bottom: -194%;
	border-bottom: 9px solid #000;
	border-bottom: 9px solid hsla(0, 0%, 20%, 0.9);
	border-top: initial;
}

/* 
.c3_cj_delBtn[data-tooltip]{
	position: absolute;
}

.c3_cj_delBtn[data-tooltip]:before {
	left: initial;
	right: -120px;
	min-width: 100px;
	max-width: 100px;
	bottom: initial;
	top: 50%;
}

.c3_cj_delBtn[data-tooltip]:after {
	bottom: -150%;
	bottom: initial;
	top: 50%;
	left: 18px;
	border-right: 9px solid red;
	border-right: 9px solid hsla(0, 0%, 20%, 0.9);
	border-top: 9px solid transparent;
	border-bottom: 9px solid transparent;
}
*/

.c3_cj_edit_dlg_cont_body .c3_cj_e_action_inputbox {
	/*background-color: #ffffff;*/
	/*border: 1px solid #dedede;*/
	/*padding: 5px !important;*/
	/*border-radius: 0 !important;*/
	/*margin: 0 !important;*/
}

.c3_cj_edit_dlg_cont_body .c3_cj_e_action_dropdownbox,
	.c3_cj_params_cont .c3_cj_e_action_dropdownbox {
	/*background-color: #ffffff;*/
	/*border: 1px solid #dedede;*/
	/*padding: 5px !important;*/
	/*border-radius: 0 !important;*/
	/*margin: 0 !important;*/
	width: initial;
}

.claro .c3_cj_edit_dlg_cont_body .dijitSelect, .claro .c3_cj_edit_dlg_cont_body .dijitTextBox
	{
	/*background-color: #ffffff;
	border: 1px solid #dedede;
	padding: 8px 0 0px 5px !important;
	height: 30px;
	border-radius: 0 !important;
	margin: 0 !important;
	width: 94%;*/
	/*color: #737373;*/
	/*font-size: 12px;*/
	/*border: 1px solid rgba(222, 222, 222, 0.5);*/
}

.claro .c3_cj_edit_dlg_cont_body .dijitSelect .dijitArrowButtonContainer,
	.claro .c3_actionf_c_maba .dijitSelect .dijitArrowButtonContainer {
	/*border: 1px solid #ffffff;
	width: 8px;
	background-color: #ffffff !important;*/
}

.c3_cj_e_s1_r1r.c3_cj_e_s1_r1r1 {
	margin: 8px 0;
}

.c3_cj_e_s1_r2r.c3_cj_e_s1_r2r1 {
	margin: 8px 0;
}

.c3_cj_item_head .c3_cj_item_title {
	color: #325671;
	font-size: 13px;
}

.c3_cj_item_head .c3_cj_v_title {
	color: #0055a9;
	font-size: 14px;
	margin: 4px 0;
}

.c3_cj_e_s1_r2r.c3_cj_e_s1_r2r1 {
	margin: 4px 0;
}

.c3_cj_e_s2_r1r.c3_cj_e_s2_r1r1 {
	margin: 4px 0;
}

.c3_cj_edit_dlg_cont_body .input-group input.c3_actionf {
	width: 99.5%;
	height: 27px;
	border-color: rgb(222, 222, 222);
	border-width: 1px;
	border-collapse: collapse;
	padding-left: 8px;
	border-top: 1px solid rgb(222, 222, 222);
}

.c3_cj_edit_dlg_cont_body .c3_actionf_item_r {
	margin: 8px 0;
}

.c3_cj_edit_dlg_cont_body .mob_prev_lbl {
	color: #8C9EAB;
	font-size: 13px;
	margin: 10px 0 4px 0;
}

.c3_cj_frm_lbl, .dijitDialogPaneContentArea label.c3_cj_frm_lbl {
	color: #8C9EAB;
	font-size: 13px;
	margin: 10px 0 4px 0;
	color: #017AC6;
	font-size: 15px;
	margin: 15px 0 4px 0;
	width: initial;
}

.c3_cj_e_s1_r1r2>div {
	margin-right: 6px;
}

.c3_cj_e_s1_r1r3>div {
  margin-right: 14px;
}

.c3_cj_e_date_cal {
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_e_date_cal>* {
	margin-left: 4px;
}
.c3_cpgnTISchdlRange .c3_cj_e_date_cal_lbl {
  margin-left: 0px;
  color: rgba(0, 0, 0, 0.81);
}
.c3_cpgnTISchdlRange .c3_cj_e_date_cal_hhmm {
  margin-left: 0px;
}
.c3_cpgnTISchdlRange .c3_tRngLbl {
  width: 35px;
}

.c3_cj_e_date_weekday_cont {
	border-left-width: 1px;
	border-left: 1px solid;
	border-color: rgba(222, 222, 222, 0.5);
}

.c3_cj_e_date_weekday {
    min-width: 32px;
    max-width: 32px;
    padding: 6px 8px;
    border: 1px solid;
    border-color: rgba(222, 222, 222, 0.5);
    border-left-width: 0px;
    border-collapse: collapse;
    cursor: pointer;
    text-align: center;
}

.c3_cj_e_date_weekday2 {
    min-width: 32px;
    max-width: 32px;
    padding: 6px 8px;
    border: 1px solid;
    border-color: #007BFF;
    border-collapse: collapse;
    cursor: pointer;
    text-align: center;
    color: #007BFF;
}

.c3_cj_e_date_weekday:hover {
	background-color: gainsboro;
}

.c3_cj_e_date_weekday2:hover {
	background-color: #007BFF;
	color: #FFFFFF;
}

.c3_cj_e_date_weekday_sel {
	background-color: gainsboro;
}

.c3_cj_e_date_weekday_sel2 {
	background-color: #007BFF;
	color: #FFFFFF;
}

.c3_cj_e_date_cal_time {
	position: relative;
	border: 0px solid;
	border-color: rgba(222, 222, 222, 0.5);
	border-collapse: collapse;
	max-height: 32px;
	min-height: 32px;
	min-width: 120px;
}

.c3_cj_e_date_cal_disp_icon {
	padding: 4px;
	cursor: pointer;
}

.c3_cj_e_date_cal_select {
	position: absolute;
	right: 20px;
	top: 96%;
	left: 0px;
	z-index: 9;
	border: 1px solid #dfdfdf;
	border: 1px solid #759dc0;
	background-color: white;
	padding: 6px;
}

.c3_cj_e_date_cal_select>div {
	padding: 4px;
	cursor: pointer;
}

.c3_cj_e_date_cal_select>div:hover {
	background-color: #f5f5f5;
}

.c3_cj_e_date_cal_select_o_best {
	border-top: 1px dotted #ebebeb;
}

.c3_cj_e_lbl {
	font-size: 13px;
	color: #017AC6;
	font-weiGHT: 600;
}

.claro .c3_cj_edit_dlg_cont_body .dijitSelect, .claro .c3_cj_edit_dlg_cont_body .dijitTextBox
	{
	/*width: 97%;*/
	width: initial;
	/*height: 30px;*/
	height: initial;
}

.claro .c3_actionf_c_maba_select_cont .dijitSelect {
	/*width: 100%;*/
}

.c3_cj_e_inputbox {
	width: 100%;
	width: initial;
}

.c3_cj_v_title {
	color: black;
	font-size: 14px;
	font-weight: 600;
}

.c3_cj_v_title-name {
	color: gray;
}

.c3_cj_hint_txt {
	color: #999;
}

.c3_cj_hint_txt span {
	color: #333;
}

.c3_cj_e_section_3 .c3_actionscanvas_frm_row_wrp {
	margin-top: 10px;
	border-radius: 0;
	height: 70px;
}

.c3_cj_e_section_3 .c3_context_p_frm_criteria_dv1 {
	width: 60px;
	background-color: rgb(247, 247, 247);
	text-align: center;
	margin: 0;
	padding-top: 5px;
	border-right: 1px solid #ddd;
}

.c3_cj_e_section_3 .c3_context_p_frm_criteria_dv_r {
	background-color: #FCFCFC;
}

.c3_cj_e_section_3 .c3_context_palette_row .c3_label_right select {
	padding-top: 3px;
	height: 31px !important;
	margin-right: 5px;
	font-size: 12px;
	background-position: 65px 12px;
}

.c3_cj_e_s2_r2r2 .c3_actionf_item_box_article_container {
	-webkit-flex: 1;
	flex: 1;
	/* background-color: bisque; */
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: column nowrap;
	flex-flow: column nowrap;
}

.c3_cj_e_s2_r2r2 div.c3_kb_content_control {
	-webkit-flex: 1;
	flex: 1;
	width: initial;
	height: initial;
	margin-left: 0;
	display: -webkit-flex;
	display: flex;
}

.c3_cj_e_s2_r2r2 div.c3_lifecycle_body_cont {
	position: inherit;
	-webkit-flex: 1;
	flex: 1;
	display: -webkit-flex;
	display: flex;
}

.c3_cj_e_s2_r2r2 .c3_actionf_maaka .c3_whitebox_channelsettings_non {
	-webkit-flex: 1;
	flex: 1;
	height: initial;
}

.claro .c3_cj_edit_dlg_cont_body .c3_tags_li .dijitTextBox {
	width: auto;
	border: 0px;
}

.c3_cj .c3_config_text_title {
	width: 100%;
}

.c3_cj .c3_cj_titleclearbox {
	width: 100%;
}

.c3_cj_titleclearbox .btn_publish {
	float: right;
	width: auto;
	padding: 6px 0;
	width: 72px !important;
	margin-left: 10px;
}

.c3_cj .c3_content_view_list {
	width: 100% !important;
}

.c3_cj .c3_settings_content_header {
	padding: 10px 0 0 0;
	overflow: initial;
}

.c3_cj .c3_content_view_list .c3_config_text_title span {
	font-size: 13px;
	padding: 3px 0;
}

.c3_cj_chart_wrp {
	-webkit-flex: 1;
	flex: 1;
}
/* */
/*.c3_cj_bargraph_container { width: 100%; }
.c3_cj_bargraph_container .c3_cj_bar {
    width: 166px;
    margin: 10px 35px 10px 9px;
    display: inline-block;
    position: relative;
    vertical-align: baseline;
}
.c3_cj_bargraph_container .c3_cj_search_item_bar {background-color: #dee8fa; padding:5px;}
.c3_cj_bargraph_container .c3_cj_convert_item_bar {background-color: #9FC2D2; padding:5px;}
*/
.c3_cj_bargraph_container {
	-webkit-flex: 1;
	flex: 1;
	-webkit-align-items: flex-end;
	align-items: flex-end;
}

.c3_cj_take_axn_cont {
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_cell {
	max-width: 150px;
	min-width: 150px;
	margin: 10px 15px 20px 12px;
	max-width: 297px;
	min-width: 297px;
	margin: 10px 0px 20px 0px;
	/*text-align: center;*/
	position: relative;
	/*background-color: aquamarine;*/
	/*border: 1px solid burlywood;*/
}

.c3_cj_cell_1 {
	-webkit-flex: 1;
	flex: 1;
	/*background-color: lightpink;*/
	position: relative;
}

.c3_cj_cell_2 {
	/*background-color: cornsilk;*/
	min-width: 24px;
	max-width: 24px;
}

.c3_cj_bargraph_container .c3_cj_bar {
	margin-bottom: 0px;
}
/*.c3_cj_bargraph_container .c3_cj_bar, .c3_cj_circle_chart_cont .c3_cj_bar {
	max-width: 150px;
	min-width: 150px;
	margin: 10px 15px 20px 12px;
	text-align: center;
	background-color: aquamarine;
}*/
.c3_cj_bargraph_container .c3_cj_bar_end {
	max-width: 150px;
	min-width: 150px;
	margin: 10px 0 10px 10px !important;
}

.c3_cj_bargraph_container .c3_cj_search_item_bar {
	background-color: #dee8fa;
	padding: 5px;
	vertical-align: bottom;
}

.c3_cj_bargraph_container .c3_cj_convert_item_bar {
	/*background-color: rgba(183, 241, 104, 0.4);*/
	background-color: rgba(242, 208, 134, 0.4);
	padding: 5px;
}

.c3_cj_chart_legendbox {
	padding: 5px 15px 8px !important;
	font-size: 12px;
	color: rgb(159, 194, 210);
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-justify-content: flex-start;
	justify-content: flex-start;
	-webkit-align-items: stretch;
	align-items: stretch;
	border-top: 1px solid rgb(229, 229, 229);
	margin-right: 15px;
}

.c3_cj_chart_legendbox .legend_box {
	clear: both;
	margin-right: 20px;
}

.c3_cj_chart_legendbox .legend_icon_circle {
	/*width:15px; height:15px;*/
	/*background: rgba(230, 250, 252, 0.53);*/
	background-color: rgba(238, 250, 209, 0.97);
	padding: 1px 9px;
	margin-right: 10px;
	border-radius: 12px;
}

.c3_cj_chart_legendbox .legend_icon_square {
	/*width:15px; height:15px;*/
	/*background: rgba(183, 241, 104, 0.4);*/
	background-color: rgba(242, 208, 134, 0.4);
	padding: 0 8px;
	margin-right: 10px;
}

.c3_cj_bargraph_container .c3_cj_bar>section {
	margin: 10px auto 0 auto;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-align-items: center;
	align-items: center;
	color: rgb(0, 85, 169);
	border: 0 solid #9FC2D2;
	padding: 8px;
	-webkit-justify-content: center;
	justify-content: center;
}

.c3_cj_legend_pos {
	-webkit-flex: 0 0 auto;
	flex: 0 0 auto;
}

.c3_cj_states_cont {
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_journey_state_outer {
	-webkit-align-items: center;
	align-items: center;
}

/*.c3_cj_states_cont .c3_rows_nowrap {
	-webkit-justify-content: flex-start !important;
	justify-content: flex-start !important;
}*/
.c3_cj_circle_chart_cont {
	-webkit-flex: 1;
	flex: 1;
	-webkit-align-items: center;
	align-items: center;
}

.c3_cj_circle_chart_cont .c3_cj_circle>section {
	/*margin: 10px auto 0 auto;*/
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-align-items: center;
	align-items: center;
	color: #fff;
	border: 0 solid #9FC2D2;
	padding: 8px;
	-webkit-justify-content: center;
	justify-content: center;
}

.c3_cj_circle_chart_cont section.c3_cj_search_item_bar {
	background-color:  #c2d2e0;
	/*background: linear-gradient(to bottom, #c2d2e0 51%, #7db9e8 100%);*/
	border: 3px solid #fff;
	-webkit-box-shadow: 0px 0px 2px 2px rgba(229,230,227,1);
	-moz-box-shadow: 0px 0px 2px 2px rgba(229,230,227,1);
	box-shadow: 0px 0px 2px 2px rgba(229,230,227,1);
	color: #fff !important; 
}

.c3_cj_circle_chart_cont .c3_cj_circle_final section.c3_cj_search_item_bar {
	background-color: #d5d5d5;
}

.c3_cj_segOuter {
	align-items: center;
	-webkit-align-items: center;
}

.c3_cj_delBtn {
	color: rgb(203, 201, 201);
	position: absolute;
	/* position: relative; */
	/* left: 114px; */
	top: 2px;
	/* width: 15px; */
	right: 2px;
	padding: 4px;
	z-index: 3;
	cursor: pointer;
}

.c3_cj_delBtn:hover  {
	color: black;
	background-color: ghostwhite;
}

.c3_cj_box1>.c3_cj_delBtn {
	visibility: hidden;
}

.c3_cj_box1:hover>.c3_cj_delBtn {
	visibility: visible;
}

.cj_stepsContainer {
	
}

.cj_stepsContainer .c3_context_pbitem_cont {
	border: 1px solid #c4cad5 !important;
	border-bottom: 4px solid #c4cad5 !important;
	height: 62px;
	width: 260px;
	margin: 10px;
	box-shadow: none !important;
	position: relative;
}

.cj_stepsContainer .c3_context_pbitem_cont .c3_palette_box_item>i {
	font-size: 25px;
	font-style: normal;
	color: #c4cad5;
}

.cj_stepsContainer .c3_context_pbitem_body i {
	align-content: center;
	align-items: center;
	border-right: 1px solid #c4cad5;
	color: #bddf8c;
	display: flex;
	font-size: 30px !important;
	height: 62px;
	justify-content: center !important;
	left: 2px;
	position: absolute;
	top: 0;
	width: 45px;
}

.cj_stepsContainer div.c3_palette_box_item_title {
	color: #7d7d7d;
	font-family: "Open Sans", sans-serif;
	font-size: 13px !important;
	font-weight: 300;
	left: 57px;
	right: 0px;
	overflow: hidden;
	position: absolute;
	text-align: left;
	text-overflow: ellipsis;
	top: -5px;
	white-space: nowrap;
	line-height: 17px;
}

.cj_c3dialog_contentArea {
	margin: 0 !important;
	padding: 0px !important;
	height: 315px !important;
	overflow-y: auto;
	clear: both !important;
	border: 0px solid #ddd;
}

.c3_cj_states_end_box {
	/*align-self: stretch;*/
	justify-content: center;
	border: 1px dashed rgba(95, 158, 160, 0.35);
	min-height: 76px;
	max-height: 76px;
	min-width: 138px;
	max-width: 138px;
	background-color: white;
	margin: 21px 0px 19px 10px;
	cursor: pointer;
}

.c3_cj_pointer {
	cursor: pointer;
}

.c3_cj_pointer:hover {
	background-color: rgba(228, 225, 225, 0.28);
}

.cj_stepsContainer .c3_segment_popstats2 {
	font-family: 'Open Sans', sans-serif;
	font-size: 20px;
	color: #91b819;
	font-weight: 300 !important;
	position: absolute;
	top: 25px;
	left: 57px;
}

.cj_stepsContainer .arrow-up {
	width: 0;
	height: 0;
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-bottom: 5px solid #4E21F2;
}

.cj_stepsContainer .arrow-down {
	width: 0;
	height: 0;
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 5px solid #f95919;
}

.cj_stepsContainer .c3_segment_text_blue {
	color: #f95919 !important;
}

.cj_stepsContainer .c3_segment_text_orange {
	color: #f95919;
}

.cj_stepsContainer .c3_segment_usageStat2 {
	font-family: 'Open Sans', sans-serif;
	font-size: 11px;
	color: #91b819;
	font-weight: 300 !important;
	position: absolute;
	top: 33px;
	right: 5px;
}

.c3_cj_seg_step_box_selected {
}
.c3_cj_selected_action_arrow {
	display: none;
}

.c3_cj_seg_step_box_selected .c3_cj_selected_action_arrow {
	display: block;
	position: absolute;
	left: 59px;
	bottom: -20px;
	z-index: 10;
	right: 0px;
	width: 0;
	height: 0;
	border-left: 12px solid transparent;
	border-right: 12px solid transparent;
	border-bottom: 12px solid rgba(159, 194, 210, 0.51);
}

.c3_cj_seg_step_box_selected .c3_cj_box1 {
	/* border: 1px solid #9FC2D2; */
	background-color: #FDFDE0;
}

.c3_cj_v_head .c3_cj_v_btn {
	color: rgb(203, 201, 201);
}

.c3_head_2col_c1 {
	min-width: 50%;
}

.c3_head_2col {
	-webkit-align-items: center;
	align-items: center;
}

.c3_head_2col_c1c.c3_head_2col_c1c1 {
	width: initial;
}

.c3_head_2col_c1c.c3_head_2col_c1c2 {
	width: initial;
	min-width: 300px;
	-webkit-flex: 1;
	flex: 1;
}

.c3_cj_e_warn_box {
	color: red;
}

.c3_all_campaignonce_box_1 {
	table-layout: fixed;
	width: 100%;
	height: initial;
}

.c3_cj_all_item_r {
}

.c3_cj_all_item_r.c3_cj_all_item_rh {
	border-bottom: 1px solid aliceblue;
}

.c3_cj_all_item_c1 {
	width: 24px;
}
.c3_cj_all_item_c11 {
	width: 24px;
}

.c3_cj_all_item_c2 {
	width: auto;
	/*width: 40%;
	width: calc(100% - 400px);*/
}

.c3_cj_all_item_c4 {
	width: 140px;
	font-size: 95%;
}

.c3_cj_all_item_c5 {
	width: 100px;
}

.c3_cj_all_item_c6 {
	width: 90px;
}
th.c3_cj_all_item_c6 .c3_cj_all_item_c_in:before {
	content: url('/res/check-icon-x17-grey.png');
	width: 16px;
	margin-right: 4px;
}

.c3_cj_all_item_c6b {
	width: 100px;
	/* hide */
	width: 0px;
}
th.c3_cj_all_item_c6b .c3_cj_all_item_c_in:before {
	content: url('/res/double-tick-x17-grey.png');
	width: 16px;
	margin-right: 4px;
}

.c3_cj_all_item_c9 {
	width: 90px;
}
th.c3_cj_all_item_c9 .c3_cj_all_item_c_in:before {
	content: url('/res/double-tick-x17-blue.png');
	width: 16px;
	margin-right: 4px;
}

.c3_cj_all_item_c10 {
	width: 90px;
}

.c3_cj_all_item_c7 {
	width: 80px;
}

.c3_cj_all_item_c8 {
	width: 0px;
	position: relative;
}

.c3_cj_all_item_r>th {
	position: relative;
}
.c3_cj_all_item_r>td {
	height: 32px;
}

.c3_cj_all_item_actions {
	display: none;
	position: absolute;
	left: -140px;
	right: 0px;
	top: 0px;
	bottom: 0px;
	background-color: rgba(171, 214, 255, 0.31);
}

.c3_cj_all_item_r:hover .c3_cj_all_item_actions {
	display: -webkit-flex;
	display: flex;
}

.c3_all_r_badge {
	font-size: 125%;
	color: #0055A9;
}

.c3_cj_badge-incomp {
	/*color: #0055A9;*/
}

.c3_cj_badge-comp {
	/*color: #0055A9;*/
}

.c3_cj_all_item_c1 .pauseBtn {
	display: none;
}

/* empty space */
.c3_cj_all_item_c_in::after {
	content: "\00a0";
	margin-left: 4px;
	min-width: 7px;
	font-size: 90%;
	/* disables text-decoration */
	display: inline-block;
}

.c3_sort_descending-true .c3_cj_all_item_c_in::after {
	content: "\2193";
}

.c3_sort_descending-false .c3_cj_all_item_c_in::after {
	content: "\2191";
}