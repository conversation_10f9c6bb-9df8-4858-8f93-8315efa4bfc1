package com.z1.factory.channeltype;

import java.util.List;
import java.util.Map;

import z1.channel.def.CredentialsDef;
import z1.commons.Utils;

import com.z1.registration.def.UrlInfoType;

public class GooglePlay implements IChannelType
{

  @Override
  public String getAccessToken()
  {
    // TODO get this info from Manish.
    return "";
  }

  @Override
  public String getFeedHandler()
  {
    return "com.z1social.channel.GPlayFeedHandler";
  }

  @Override
  public List<Map<String, Object>> getChannelInfoByCompanyName(
      Map<String, Object> channelInput)
  {
    // TODO remove the hardcoded info
    
  return null;
  }

  @Override
  public List<Map<String, Object>> getChannelInfoByUrl(
      Map<String, Object> channelInput)
  {
    // TODO remove the hardcoded info
    return null;
  }
  
  @Override
  public void setCredentialDef(UrlInfoType channelUrl, CredentialsDef cd)
  {
    String userName = Utils.getPropertyValue("google.username");
    String password = Utils.getPropertyValue("google.password");
    cd.setUsername(userName);
    cd.setPassword(password);
    
  }
  
  
}
