package com.z1.ml;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.c3.EventInfo;
import z1.ml.DNAEventList;

/**
 * A class that is used for the management of the DNA Event List.
 */
public class DNAEventListManager
{
  private DNAEventList dnaEventList;
  private UContext uctx;

  public DNAEventListManager(UContext uctx)
  {
    this.dnaEventList = new DNAEventList(uctx);
    this.uctx = uctx;
  }

  /**
   * Get a list of namespace events that are explicitly added to the DNA Event
   * List.
   * 
   * @return
   */
  public List<String> getAllExplicitlyAdded()
  {
    return this.dnaEventList.getAllExplicitlyAddedDNAEvents();
  }

  /**
   * Removes given namespace events from the DNA Event List.
   * 
   * @param namespaceEventsToRemove
   * @throws DNAEventListManagerException
   */
  public void removeFromDNAEventList(List<String> namespaceEventsToRemove)
      throws DNAEventListManagerException
  {
    // Validate that the input is not empty.
    if (CollectionUtils.isEmpty(namespaceEventsToRemove))
    {
      throw new DNAEventListManagerException("Input list is empty.");
    }

    // Validate that the input has no duplicate events.
    if (namespaceEventsToRemove.size() != namespaceEventsToRemove.stream()
        .distinct().count())
    {
      throw new DNAEventListManagerException(
          "Input list contains duplicate namespace events to remove.");
    }

    // Validate that the dna event list has events.
    List<String> dnaEvents = this.dnaEventList.getAllExplicitlyAddedDNAEvents();
    if (dnaEvents.isEmpty())
    {
      throw new DNAEventListManagerException(
          "There is nothing to remove because the DNA Event List is empty.");
    }

    // Validate that the namespace events to are in the dna event list.
    List<String> namespaceEventDoesNotExistInDNAEventList = namespaceEventsToRemove
        .stream().filter(eventToRemove -> !dnaEvents.contains(eventToRemove))
        .collect(Collectors.toList());
    if (!namespaceEventDoesNotExistInDNAEventList.isEmpty())
    {
      JsonMarshaller jm = new JsonMarshaller();
      throw new DNAEventListManagerException(
          "Unable to remove the following namespace events because they do not exist in the DNA Event List: "
              + jm.serialize(namespaceEventDoesNotExistInDNAEventList));
    }

    this.dnaEventList.removeFromDNAEventList(namespaceEventsToRemove);
  }

  /**
   * Adds given namespace events to the DNA Event List.
   * 
   * @param namespaceEventsToAdd
   * @param force
   *          if set to true then the namespaceEventsToAdd becomes the new DNA
   *          Event List.
   * @throws DNAEventListManagerException
   */
  public void addToDNAEventList(List<String> namespaceEventsToAdd, boolean force)
      throws DNAEventListManagerException
  {
    // Validate that the input is not empty.
    if (CollectionUtils.isEmpty(namespaceEventsToAdd) && !force)
    {
      throw new DNAEventListManagerException("Input list is empty.");
    }

    // Validate that the input list does not contain duplicate events.
    if (namespaceEventsToAdd.size() != namespaceEventsToAdd.stream().distinct()
        .count())
    {
      throw new DNAEventListManagerException(
          "Input list contains duplicate namespace events to add.");
    }

    // Validate that the list of events to add already exists in the system
    EventInfo eventInfo = new EventInfo(this.uctx);
    List<String> namespaceEventsThatDoNotExist = namespaceEventsToAdd.stream()
        .filter(namespaceEventToAdd -> !eventInfo.isKnownEvent(namespaceEventToAdd))
        .collect(Collectors.toList());
    if (!namespaceEventsThatDoNotExist.isEmpty())
    {
      JsonMarshaller jm = new JsonMarshaller();
      throw new DNAEventListManagerException(
          "Unable to add the following namespace events because they do not exist in the system: "
              + jm.serialize(namespaceEventsThatDoNotExist));
    }

    // Validate that the namespace events to add are not already in the dna
    // event list
    List<String> dnaEvents = this.dnaEventList.getAllExplicitlyAddedDNAEvents();
    List<String> namespaceEventAlreadyExists = dnaEvents.stream()
        .filter(dnaEventName -> namespaceEventsToAdd.contains(dnaEventName))
        .collect(Collectors.toList());
    if (!namespaceEventAlreadyExists.isEmpty() && !force)
    {
      JsonMarshaller jm = new JsonMarshaller();
      throw new DNAEventListManagerException(
          "Unable to add the following namespace events because they already exist in the DNA Event List: "
              + jm.serialize(namespaceEventAlreadyExists));
    }

    if (force)
    {
      this.dnaEventList.setDNAEventList(namespaceEventsToAdd);
    }
    else
    {
      this.dnaEventList.addToDNAEventList(namespaceEventsToAdd);
    }
  }
}
