package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.Utils.ResponseMessage.Status;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.cache.ObjectCache;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobQueue;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.analytics.AnalyticSubsystem;
import z1.analytics.QueryOptimizer;
import z1.analytics.commons.AnalyticsUtils;
import z1.analytics.query.AnalyticQueryBuilder;
import z1.analytics.query.def.AnalyticQueryDef;
import z1.analytics.segment.SegmentSubsystem.Axis;
import z1.analytics.segment.SegmentSubsystem.MetricName;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.Journey;
import z1.c3.Journey.Type;
import z1.c3.Segment;
import z1.c3.def.JourneyDef;
import z1.c3.def.SegmentDef;
import z1.c3.def.StateDef;
import z1.commons.Const;
import z1.commons.FileLoader;
import z1.commons.FileLoader.ObjType;
import z1.commons.FileLoader.OpType;
import z1.commons.RunStatus;
import z1.commons.RunStatus.QueryType;
import z1.commons.Utils;
import z1.commons.def.RuleDef;
import z1.core.Context;
import z1.core.profile.ProfileUtil;
import z1.core.utils.FileLoaderStats;
import z1.core.utils.TimeUtils;
import z1.domains.DomainHandler;
import z1.stats.ProfileCountStat;
import z1.stats.SegmentLoaderRecorder;
import z1.stats.SegmentStatsRecorder;
import z1.template.JourneyDesignUtils;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;
import z1.users.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SegmentsHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    stats,
    id,
    list,
    users,
    ulStats,
    monthly,
    references,
    recountPercent,
    countStatus,
    segmentCountTime,
    segNewEvalEnabled,
    selector
  }

  private enum PostCommand
  {
    create,
    update,
    delete,
    upload,
    deleteUploadStats,
    download,
    dayChange,
    setDailyTotal,
    setDailyChange,
    recount,
    resetRunStatus,
    stopCounting,
    dynamicEvaluation,
    suspend,
    resume,
    evaluateOnlyIfUsed,
    convertToUpload,
    countStatuses,
    ids
  }

  private enum Fields
  {
    id,
    name,
    icon,
    population,
    creationTime,
    createdBy,
    lastUpdatedTime,
    lastUpdatedBy,
    isCounting,
    percentCompletion,
    isUpload,
    evaluateOnlyIfUsed,
    inline,
    type
  }

  private enum FilterOption
  {
    dynamicEvalEnable,
    dynamicEvalDisable,
    all,
    inline,
    noninline
  }

  private final List<String> metricsToQuery = Arrays.asList(
      MetricName.totalPopulation.name(), MetricName.lastCountTime.name(),
      MetricName.lastCountDuration.name());

  // Common metric fields used when querying for countStatuses.
  private final Map<String, Object> commonparams = Stream
      .of(new AbstractMap.SimpleEntry<>("subsystem",
          AnalyticSubsystem.SubsystemType.segment.name()),
          new AbstractMap.SimpleEntry<>("datasetName", "countStatuses"),
          new AbstractMap.SimpleEntry<>("timeUnit",
              AnalyticsUtils.TimeUnit.none.name()),
          new AbstractMap.SimpleEntry<>("aggregateAs", "SUM"),
          new AbstractMap.SimpleEntry<>("groupBy",
              Collections.singletonList(Axis.segmentId.name())))
      .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/segments/
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/segments/all => Sends all segments without stats
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);
            String inlineValue = req.getParameter("inline");
            // We'll get all goal definitions to send them back
            List<Segment> segs = Segment.loadAll(uctx, true);
            for (Segment s : segs)
            {
              if (s == null
                  || TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX))
              {
                continue;
              }

              boolean isUpload = z1.commons.Utils.isUploadedSegment(s);
              boolean evaluateOnlyIfUsed = z1.commons.Utils
                  .isEvaluateOnlyIfUsedSegment(s);

              Map<String, Object> map = s.getDefAsMap();
              if (map != null)
              {
                String inline = s.getInline();
                if (inlineValue != null)
                {
                  if ("true".equals(inlineValue))
                  {
                    // skip non inline segments
                    if (!s.isInline()) continue;
                    map.put(Fields.inline.name(), inline);
                  }
                  // skip inline segments
                  else if (s.isInline())
                  {
                    continue;
                  }
                }
                // inlineValue is null then return all segments including inline
                map.put("id", s.getId());

                Long creationTime = s.getCreationTime();
                if (creationTime != null)
                {
                  map.put(Fields.creationTime.name(), creationTime);
                }

                String createdBy = s.getCreatedBy();
                if (createdBy != null)
                {
                  map.put(Fields.createdBy.name(), createdBy);
                }

                Long lastUpdatedTime = s.getLastUpdatedTime();
                if (lastUpdatedTime != null)
                {
                  map.put(Fields.lastUpdatedTime.name(), lastUpdatedTime);
                }
                String lastUpdatedBy = s.getLastUpdatedBy();
                if (lastUpdatedBy != null)
                {
                  map.put(Fields.lastUpdatedBy.name(), lastUpdatedBy);
                }

                map.put(Fields.isUpload.name(), isUpload);
                map.put(Fields.evaluateOnlyIfUsed.name(), evaluateOnlyIfUsed);
                map.put(Fields.type.name(), "segment");
                //Add the isAudience flag
                map.put("isAudience",s.isAudienceSegment());
                ret.add(map);
              }
            }

            if (!"true".equals(inlineValue))
            {
              Map<String, Object> map = new java.util.HashMap<>();
              map.put("id", Const.ALL_USERS);
              map.put("name", "All Users");
              map.put("icon", "fa-users");
              ret.add(map);
            }
            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);

            break;
          }
          // c3/data/segments/stats => returns stats for each segment ID
          case stats:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);
            SegmentStatsRecorder ssr = new SegmentStatsRecorder(uctx);

            // We'll get all goal definitions to send them back
            List<Segment> segs = Segment.loadAll(uctx, true);
            segs.stream().filter(Objects::nonNull).forEach(s -> {
              Map<String, Object> map = new HashMap<>(3);
              if (s.getDefAsMap() != null)
              {
                String segmentId = s.getId();
                map.put("id", segmentId);
                long todayTotal = SegmentStatsRecorder.getTotalPopulation(uctx,
                    segmentId);
                map.put("population", (todayTotal < 0) ? "?" : todayTotal);
                long prevTotal = ssr.getDailyTotal(segmentId, 1);
                if (prevTotal <= 0)
                {
                  // try 2 days ago
                  prevTotal = ssr.getDailyTotal(segmentId, 2);
                }
                float percent = (prevTotal <= 0) ? 0
                    : (todayTotal - prevTotal) * 100F / prevTotal;
                map.put("percent", percent);
                ret.add(map);
              }
            });

            Map<String, Object> allUserMap = new java.util.HashMap<>(2);
            allUserMap.put("id", Const.ALL_USERS);
            long totalCnt = ProfileCountStat.getCount(uctx);
            allUserMap.put("population", (totalCnt < 0) ? "?" : totalCnt);
            long prevTotal = ProfileCountStat.getDailyTotal(uctx, 1);
            if (prevTotal <= 0)
            {
              // try 2 days ago
              prevTotal = ProfileCountStat.getDailyTotal(uctx, 2);
            }
            float percent = (prevTotal <= 0 || totalCnt <= 0
                || (totalCnt - prevTotal) <= 0) ? 0
                    : (totalCnt - prevTotal) * 100F / prevTotal;
            allUserMap.put("percent", percent);
            ret.add(allUserMap);

            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);
            break;
          }
          // c3/data/segments/list[?type=<filter>] => Sends all segments along
          // with "All Users"
          case list:
          {
            String filter = req.getParameter("type");
            FilterOption p = FilterOption.all;
            if (filter != null)
            {
              p = FilterOption.valueOf(filter);

            }
            String payload = new JsonMarshaller()
                .serialize(getSegmentsList(uctx, p));
            resp.getWriter().print(payload);

            break;
          }
          // c3/data/segments/users?sId=<segment-id> => Sends profile data of
          // users
          // of a segment
          case users:
          {
            String sId = req.getParameter("sId");
            long hours = 24;
            long maxRows = 100;
            List<Map<String, Object>> ret = new ProfileUtil()
                .getSegmentProfiles(Context.getInstance(uctx),
                    Segment.PREFIX + "+" + sId, hours, maxRows);
            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);
            break;
          }

          // c3/data/segments/id?sId=<segment-id>
          case id:
          {
            // This must be a request for a specific goal payload.
            String sId = req.getParameter("sId");
            // get the goal payload
            Segment s = Segment.load(uctx, sId, true);

            if (s != null && s.isAudienceSegment())
            {
              // Default to editable
              boolean isEditable = true;

              Map<String, Object> pl2Map = Const.jsonMarshaller.readAsMap(s.getPayload2());
              if (pl2Map != null && pl2Map.containsKey("experienceList"))
              {
                List<String> exps = (List<String>) pl2Map.get("experienceList");
                isEditable = exps == null || exps.isEmpty();
              }

              Map<String, Object> sdef = s.getDefAsMap();
              sdef.put("isEditable", isEditable);

              if (s.isAudienceSegment())
              {
                sdef.put("hasCustomEppRules", s.hasCustomEppRules());
              }

              resp.getWriter().print(Const.jsonMarshaller.serialize(sdef));
              return;
            }
            else
            {
              String payload = new JsonMarshaller().serialize(s != null ? s.getDef() : null);
              resp.getWriter().print(payload);
            }
            break;
          }

          // c3/data/segments/ulStats?segmentName=<segment-name>[&opType=<op-type>]
          case ulStats:
          {
            String segmentName = req.getParameter("segmentName");
            String opTypeReq = req.getParameter("opType");
            OpType opType = null;

            if (opTypeReq != null)
            {
              opType = OpType.valueOf(opTypeReq);
            }

            // getting upload status
            Map<String, Object> ret = _updateUploadSegmentStatus(uctx,
                segmentName, opType);
            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;
          }
          // c3/data/segments/monthly?segmentId=<segment-id>&yearMonth=<year-month>
          // (yearMonth in YYYYMM format)
          // --> returns list of daily total population for the querying month.
          case monthly:
          {
            String segmentId = req.getParameter("segmentId");
            String yearMonth = req.getParameter("yearMonth");

            SegmentStatsRecorder ssr = new SegmentStatsRecorder(uctx);

            List<Map<String, Object>> dailyTotals = ssr
                .getDailyTotalForMonth(segmentId, yearMonth);

            String today = new TimeUtils().getDate();
            String todayMonthYear = today.substring(0, 6);
            if (todayMonthYear.equals(yearMonth))
            {
              // Get today value
              Long todayTotal = SegmentStatsRecorder.getTotalPopulation(uctx,
                  segmentId);
              String todayDate = today.substring(6);
              Map<String, Object> todayItem = new HashMap<>(2);
              todayItem.put("time", todayDate);
              todayItem.put("views", todayTotal);
              if (dailyTotals == null)
              {
                dailyTotals = new ArrayList<>();
              }
              dailyTotals.add(todayItem);
            }

            Map<String, Object> res = new HashMap<>(2);
            res.put("identifier", "id");
            res.put("items", dailyTotals);

            resp.getWriter().print(new JsonMarshaller().serialize(res));
            break;
          }

          // c3/data/segments/references?sid=<signal-id>
          case references:
          {
            String sid = req.getParameter("sid");
            List<Object> result = _getReferences(uctx, sid);
            resp.getWriter().print(new JsonMarshaller().serialize(result));
            break;
          }

          // c3/data/segments/recountPercent
          case recountPercent:
          {
            Map<String, Object> status = RunStatus.getStatus(uctx,
                QueryType.segment, uctx.getNamespace());
            status.remove("isCounting");
            resp.getWriter().print(new JsonMarshaller().serialize(status));
            break;
          }

          // c3/data/segments/countStatus[?segment=<req-seg-id>]
          case countStatus:
          {
            String reqSegId = req.getParameter("segment");
            String countId = uctx.getNamespace(); // count all ID
            boolean isUpload = false;

            if (reqSegId == null || reqSegId.isEmpty())
            {
              reqSegId = Const.ALL_USERS;
            }

            if (!reqSegId.equals(Const.ALL_USERS))
            {
              countId = uctx.getNamespace() + "_" + reqSegId;

              Segment segment = Segment.load(uctx, reqSegId, true);

              // Nothing to process if segment was not found.
              if (segment == null)
              {
                Map<String, Object> result = new HashMap<>();
                result.put("id", reqSegId);
                result.put("totalSoFar", "0");
                resp.getWriter().print(new JsonMarshaller().serialize(result));
                return;
              }

              isUpload = Utils.isUploadedSegment(segment);
            }

            // Query segment info.
            Map<String, Object> segMetric = _getUniqueSegmentInfo(uctx,
                reqSegId);

            Map<String, Object> result = getSegmentInfoForUI(uctx, segMetric,
                reqSegId, isUpload);

            resp.getWriter().print(new JsonMarshaller().serialize(result));
            break;
          }
          // c3/data/segments/segmentCountTime[?segment=<req-seg-id>]
          case segmentCountTime:
          {
            String reqSegId = req.getParameter("segment");
            Map<String, Object> lastCountTime = (reqSegId == null
                || reqSegId.length() == 0)
                    ? ProfileCountStat.getLastRecountTime(uctx)
                    : SegmentStatsRecorder.getLastRecountTime(uctx, reqSegId);

            Map<String, Object> res = new HashMap<>();
            res.put("lastRunStart", (lastCountTime == null) ? -1
                : (Number) lastCountTime.get("lastRunStart"));
            res.put("lastRunEnd", (lastCountTime == null) ? -1
                : (Number) lastCountTime.get("lastRunEnd"));
            resp.getWriter().print(new JsonMarshaller().serialize(res));
            break;
          }
          case selector:
          {
            String segType = pathParts[1];
            if (!"insession".equalsIgnoreCase(segType))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Invalid segment type.");
              resp.getWriter().print(msg);
              return;
            }

            resp.getWriter().print(Const.jsonMarshaller.serialize(
                DomainHandler.instance(uctx).getInSessionSegRuleSelectors()));

            break;
          }
          default:
          {
            break;
          }
        }

      }

      /**
       * Gets segment list
       * 
       * @param uctx
       * @param fo
       * @return
       */
      private List<Map<String, Object>> getSegmentsList(UContext uctx,
          FilterOption fo)
      {
        List<Map<String, Object>> ret = new java.util.ArrayList<>(20);
        List<Segment> segs = Segment.loadAll(uctx, true).stream()
            .filter(Objects::nonNull).collect(Collectors.toList());

        switch (fo)
        {
          case dynamicEvalDisable:
          {
            for (Segment s : segs)
            {
              if (!s.getDef().isActive()
                  && !TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX))
              {
                ret.add(_getSegmentInfo(s));

              }
            }
            return ret;
          }
          case dynamicEvalEnable:
          {
            for (Segment s : segs)
            {
              if (s.getDef().isActive()
                  && !TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX))
              {
                ret.add(_getSegmentInfo(s));
              }
            }
            break;
          }
          case inline:
          {
            for (Segment s : segs)
            {
              if (s.isInline()
                  && !TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX))
              {
                ret.add(_getSegmentInfo(s));
              }
            }
            return ret;
          }
          case noninline:
          {
            for (Segment s : segs)
            {
              if (!s.isInline()
                  && !TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX))
              {
                ret.add(_getSegmentInfo(s));
              }
            }
            break;
          }
          default:
          {
            for (Segment s : segs)
            {
              if (!TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX))
              {
                ret.add(_getSegmentInfo(s));
              }
            }
          }
        }

        // Add all users
        Map<String, Object> map = new java.util.HashMap<>();
        map.put("id", Const.ALL_USERS);
        map.put("name", "All Users");
        map.put("icon", "fa-users");
        ret.add(map);
        return ret;
      }
    };

  }

  /**
   * @param s
   * @return
   */
  private Map<String, Object> _getSegmentInfo(Segment s)
  {
    Map<String, Object> map = new java.util.HashMap<>();

    map.put("id", s.getId());
    map.put("name", s.getDef().getName());
    if (s.getDef().getDescription() != null)
    {
      map.put("description", s.getDef().getDescription());
    }
    map.put("icon", s.getDef().getIcon());
    map.put("isAudience",s.isAudienceSegment());
    
    if (s.isAudienceSegment())
    {
      map.put("hasCustomEppRules", s.hasCustomEppRules());
    }
    return map;
  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings({ "incomplete-switch", "unchecked" })
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        boolean invalidateCache = true;

        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        switch (command)
        {
          // c3/data/segments/create[?inline=<inline>] => payload has the
          // definition
          // returns <segment id>
          case create:
          {
            if (RunStatus.isRunning(uctx, QueryType.segment,
                uctx.getNamespace()))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  "Counting for all segments is in progress. "
                      + "Please create/update segment only when counting is done.");
              resp.getWriter().print(msg.toString());
              return;
            }

            // Create a segment by loading the payload from the request
            String inline = req.getParameter("inline");
            boolean isInline = false;
            if (inline != null)
            {
              isInline = Boolean.parseBoolean(inline);
            }
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(payload);
            String name = map.get("name").toString();
            if (Segment.isSegmentExistWithName(uctx, name))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  "Segment already exists with name: " + name);
              resp.getWriter().print(msg.toString());
              return;
            }
            Segment s = Segment.create(uctx);
            if (isInline)
            {
              map.put("active", false);
              s.setPayload(jm.serializeMap(map), "inline=true");
            }
            else
            {
              s.setPayload(payload);
            }
            // set createdBy attribute in palyload2
            Map<String, Object> pl2Map = (Map<String, Object>) map
                .get("payload2");
            if (pl2Map != null)
            {
              pl2Map.put("createdBy",
                  uctx.getUser().getValues().get(User.ROLE));
            }
            else
            {
              pl2Map = new HashMap<>(1);
              pl2Map.put("createdBy",
                  uctx.getUser().getValues().get(User.ROLE));
            }
            s.setPayload2(jm.serialize(pl2Map));
            s.save();
            String sid = s.getId();

            Map<String, Object> params = new HashMap<String, Object>(10);
            params.put("id", sid);
            ObjectCache.getInstace(uctx).invalidateAll();

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.segmentCreateOrUpdateSuccess,
                "Segment create/update done!", params);
            resp.getWriter().print(msg.toString());

            ArtifactAudit
              .newInstance(uctx,
                s.isAudienceSegment() ? ItemTypes.audience
                  : ItemTypes.segment,
                sid, name, Operations.create)
              .save();
            break;
          }
          // c3/data/segments/update?sid=<segment-id>[&inline=<inline>] =>
          // payload has the definition
          case update:
          {
            if (RunStatus.isRunning(uctx, QueryType.segment,
                uctx.getNamespace()))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  "Counting for all segments is in progress. "
                      + "Please create/update segment only when counting is done.");
              resp.getWriter().print(msg.toString());
              return;
            }

            // Create a segment by loading the payload from the request
            String sid = req.getParameter("sid");
            String inline = req.getParameter("inline");
            boolean isInline = false;
            if (inline != null)
            {
              isInline = Boolean.parseBoolean(inline);
            }
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(payload);
            String name = map.get("name").toString();

            Segment s = Segment.load(uctx, sid, true);
            if (s == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  "Unable to load the segment defintion for segment id" + sid);
              resp.getWriter().print(msg.toString());
              return;
            }

            // check if segment has flag isEditable set to false
            // if isEditable flag is set to false, prevent update
            if (s.getDef().isEditable() != null && !s.getDef().isEditable())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  "Cannot update segment with isEditable flag set to false "
                      + name);
              resp.getWriter().print(msg.toString());
              return;
            }

            // check if updated segment name changes then check if existing
            // segments with name
            // if present return failed response message
            if (Segment.isSegmentExistWithName(uctx, name)
                && !s.getDef().getName().equals(name))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  "Segment already exists with name: " + name);
              resp.getWriter().print(msg.toString());
              return;
            }
            if (isInline)
            {
              map.put("active", false);
              s.setPayload(jm.serializeMap(map), "inline=true");
            }
            else
            {
              s.setPayload(payload);
            }

            // update payload2
            Map<String, Object> pl2Map = (Map<String, Object>) map
                .get("payload2");
            String existingPl2Str = s.getPayload2();
            if (pl2Map != null)
            {
              if (existingPl2Str != null && !existingPl2Str.isEmpty())
              {
                Map<String, Object> existingPl2Map = jm
                    .readAsMap(existingPl2Str);
                existingPl2Map.putAll(pl2Map);
                s.setPayload2(jm.serialize(existingPl2Map));
              }
              else
              {
                s.setPayload2(jm.serialize(pl2Map));
              }
            }

            int validationCode = JourneyDesignUtils.validateRules(uctx, s);

            if (validationCode != 0)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.segmentCreateOrUpdateFailed,
                  JourneyDesignUtils
                      .generateFieldConflictErrorMsg(validationCode));
              resp.getWriter().print(msg.toString());
              return;
            }

            s.save();

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.segmentCreateOrUpdateSuccess,
                "Segment create/update done!");
            resp.getWriter().print(msg.toString());

            ArtifactAudit.newInstance(uctx,
              s.isAudienceSegment() ? ItemTypes.audience : ItemTypes.segment,
              sid, name, Operations.edit).save();

            break;
          }
          // c3/data/segments/delete?sid=<segment-id>
          case delete:
          {
            String sid = req.getParameter("sid");
            req.setAttribute("id", sid);
            req.setAttribute("type", "segment");
            String[] subParts = new String[pathParts.length + 1];
            subParts[0] = "segment";
            subParts[1] = pathParts[0];

            new BXHandler().post().handle(uctx, subParts, req, resp);

            break;
          }
          // c3/data/segments/<suspend|resume>?sid=<segment-id>
          case suspend:
          case resume:
          {
            String sid = req.getParameter("sid");
            String[] idType = sid.split("\\+");
            String subtype = idType[0];

            Segment s = Segment.load(uctx, sid, true);
            if (s == null) return;
            String payload2 = s.getPayload2();
            Map<String, Object> ret = TemplateUtils
                .getModuleConfigInstanceReferences(uctx, payload2);
            if (ret == null || !ret.containsKey("type")) return;
            TemplateConst.InstanceConfigType ict = TemplateConst.InstanceConfigType
                .valueOf((String) ret.get("type"));
            Set<String> refs = (Set<String>) ret.get("refs");

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            if (ict.equals(TemplateConst.InstanceConfigType.primary))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "This enrichment code is primary component of a module instance. Operation '"
                        + command.name()
                        + "' finished and was also applied on its supportive components: "
                        + references);
              }

            }
            else if (ict.equals(TemplateConst.InstanceConfigType.supportive))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "This segment is supportive config instance and still being referenced in: "
                        + references);
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            Artifact art = new Artifact();
            art.setSubtype(subtype);
            art.setId(sid);
            TemplateArtifact ta = new TemplateArtifact(uctx, null, art, null,
                null, subtype, null);

            ModuleVisitor.VisitingOperation op;
            if (command.equals(PostCommand.suspend))
            {
              op = ModuleVisitor.VisitingOperation.suspend;
            }
            else
            {
              op = ModuleVisitor.VisitingOperation.resume;
            }

            ModuleVisitor mv = new ModuleVisitor(uctx, null, op);
            mv.visit(ta);
            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            resp.getWriter().print(msg.toString());

            break;
          }
          // c3/data/segments/upload?segmentName=<segment-name>&skipSegmentCache=true
          // flag skipSegmentCache is optional
          // Set the flag to true to skip all the inline notify clear cache
          // this also blocks the call to clear cache at the end of the
          // processing
          case upload:
          {
            String segmentName = req.getParameter("segmentName");
            String skipSegmentCache = req.getParameter("skipSegmentCache");

            if (Boolean.parseBoolean(skipSegmentCache))
            {
              uctx.put(Const.SKIP_INLINE_NOTIFY_CLEAR_CACHE,
                  Boolean.TRUE.toString());
              invalidateCache = false;
            }

            List<FileItem> items = new ServletFileUpload(
                new DiskFileItemFactory()).parseRequest(req);
            byte[] fileContent = null;

            String fileName = "";
            for (FileItem item : items)
            {
              String fldName = item.getFieldName();
              if ("file".equals(fldName))
              {
                fileContent = item.get();
              }
              if ("name".equals(fldName))
              {
                fileName = new String(item.get());
              }
            }
            if (fileName.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.uploadFailed,
                  "name attribute is missing");
              resp.getWriter().print(msg.toString());
              return;
            }

            if (!Utils.getFileExtension(fileName).equals("csv"))
            {
              resp.getWriter()
                  .print(new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidPayload,
                      "Only file type csv is valid."));

              return;
            }

            String segId = FileLoader.createSegment(uctx, segmentName);
            if (null == segId)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.uploadFailed,
                  "Attribute/activity based segment already exist with name "
                      + segmentName);
              resp.getWriter().print(msg.toString());
              return;
            }

            // set createdBy attribute in payload2
            Map<String, Object> pl2Map = new HashMap<>(1);
            pl2Map.put("createdBy", uctx.getUser().getValues().get(User.ROLE));
            Segment s = Segment.load(uctx, segId, true);
            s.setPayload2(new JsonMarshaller().serialize(pl2Map));
            s.save();

            InputStream is = new ByteArrayInputStream(fileContent);
            // launch new thread to handle work
            Thread uploadT = new Thread(new FileLoader(uctx, OpType.upload,
                ObjType.segment, segId, is, fileName, null));
            uploadT.setDaemon(true);
            uploadT.start();
            Map<String, Object> params = new HashMap<>(1);
            params.put("segmentId", segId);

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.processing,
                ResponseMessage.Type.uploadProcessing,
                "Processing upload. Monitor progress by selecting 'View Upload Status'",
                params);
            resp.getWriter().print(msg.toString());

            ArtifactAudit.newInstance(uctx, ItemTypes.segment, segId,
                segmentName, Operations.create).save();

            break;
          }

          // c3/data/segments/deleteUploadStats?segmentId=<segment-id>&fileName=<file-name>
          case deleteUploadStats:
          {
            // delete all upload stats records for a specific segment
            String segmentId = req.getParameter("segmentName");
            String fileName = req.getParameter("fileName");
            String timestamp = req.getParameter("timestamp");
            FileLoaderStats.deleteStatsFromCubeAndMongo(uctx, segmentId,
                fileName, timestamp, OpType.upload);

            break;
          }

          // c3/data/segments/download?segmentName=<segment-name>
          case download:
          {
            // This is a request to download a specific entity.
            String segmentName = req.getParameter("segmentName");
            Thread downloadT = new Thread(new FileLoader(uctx, OpType.download,
                ObjType.segment, segmentName, null, null));
            downloadT.setDaemon(true);
            downloadT.start();

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.processing,
                ResponseMessage.Type.downloadProcessing,
                "Processing download. Monitor progress by selecting 'View Download Status'");
            resp.getWriter().print(msg.toString());

            break;
          }
          // Note (dp): For generate test data and/or fix data.
          // We are not exposing this
          // c3/data/segments/setDailyTotal?segmentId=<segment-id>&yearMonthDate=<yearMonthDate>&count=<count>
          case setDailyTotal:
          {
            String segmentId = req.getParameter("segmentId");
            String yearMonthDate = req.getParameter("yearMonthDate");
            Long count = Long.parseLong(req.getParameter("count"));
            SegmentStatsRecorder ssr = new SegmentStatsRecorder(uctx);
            ssr.setDailyTotal(uctx, segmentId, yearMonthDate, count);
            break;
          }
          /***********************************************************/
          // Note (dp): For generate test data and/or fix data
          // We are not exposing this

          // c3/data/segments/setDailyChange?segmentId=<segment-id>&yearMonthDate=<yearMonthDate>&count=<count>
          case setDailyChange:
          {
            String segmentId = req.getParameter("segmentId");
            String yearMonthDate = req.getParameter("yearMonthDate");
            Long count = Long.parseLong(req.getParameter("count"));

            SegmentStatsRecorder ssr = new SegmentStatsRecorder(uctx);
            ssr.setDailyChange(uctx, segmentId, yearMonthDate, count);
            break;
          }

          // c3/data/segments/recount?segment=<req-seg-id>
          case recount:
          {
            // String forceStr = req.getParameter("force");
            // boolean forceRecount = "true".equalsIgnoreCase(forceStr);

            List<String> sIds = new ArrayList<>(1);
            ResponseMessage msg = null;

            // Check if the request param sends a segment ID
            // Note: Only allow request to recount ALL or recount ONE
            String reqSegId = req.getParameter("segment");
            if (reqSegId == null || reqSegId.length() == 0)
            {
              List<Segment> segs = Segment.loadAll(uctx, true);
              // Skip any uploaded or suspended segment
              sIds = segs.stream().filter(Objects::nonNull)
                  .filter(seg -> !(z1.commons.Utils.isUploadedSegment(seg)
                      || seg.isSuspended()))
                  .map(seg -> seg.getId()).collect(Collectors.toList());
            }
            else
            {
              Segment seg = Segment.load(uctx, reqSegId);
              if (z1.commons.Utils.isUploadedSegment(seg) || seg.isSuspended())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.segmentRecountFailed,
                    "Selected segment is either an uploaded segment or has been suspended and cannot be recounted.");
                resp.getWriter().print(msg.toString());
                return;
              }
              sIds.add(reqSegId);

              if (seg != null)
              {
                ArtifactAudit.newInstance(uctx, ItemTypes.segment, seg.getId(),
                    seg.getDef().getName(), Operations.recount).save();
              }
            }

            if (sIds == null || sIds.isEmpty())
            {
              uctx.getLogger(getClass()).warning("No segment found to recount");
              return;
            }

            boolean recountAll = reqSegId == null || reqSegId.length() == 0;

            msg = _recountSegments(uctx, sIds, recountAll);
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/segments/resetRunStatus?segment=<req-seg-id>
          case resetRunStatus:
          {
            String reqSegId = req.getParameter("segment");
            String countId = uctx.getNamespace(); // count all ID
            if (reqSegId != null && reqSegId.length() > 0) // count individual
                                                           // segment
            {
              countId += "_" + reqSegId;
            }

            if (RunStatus.isRunning(uctx, QueryType.segment, countId))
            {
              RunStatus.resetOperation(QueryType.segment, uctx.getNamespace(),
                  true);
            }

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.segmentRecountProcessing,
                "Run status for segment recount is reset");
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/segments/stopCounting?segment=<req-seg-id>
          case stopCounting:
          {
            String message = null;
            ResponseMessage msg = null;

            String reqSegId = req.getParameter("segment");
            String countId = uctx.getNamespace(); // count all ID
            if (reqSegId != null && reqSegId.length() > 0) // count individual
                                                           // segment
            {
              countId += "_" + reqSegId;
            }

            RunStatus.stopOperation(QueryType.segment, countId, true);
            message = "Stop status for segment counting for " + countId + ": "
                + RunStatus.isStopped(uctx, QueryType.segment, countId);
            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.segmentRecountProcessing, message);

            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/segments/dynamicEvaluation?id=<segmentId>&enable=<true|false>
          case dynamicEvaluation:
          {
            String id = req.getParameter("id");
            String flag = req.getParameter("enable");
            ResponseMessage msg = null;
            if (id == null || id.isEmpty() || flag == null || flag.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'id' or 'enable'.");
              resp.getWriter().print(msg.toString());
              return;
            }

            id = "segment+" + id;
            boolean enabled = false;
            try
            {
              enabled = Boolean.parseBoolean(flag);

            }
            catch (Exception e)
            {
              uctx.getLogger(getClass())
                  .warning(String.format(
                      "Failed to parse value of parameter 'enable'. Reason: %s",
                      e.toString()));
            }

            Segment.setDynamicEvaluation(uctx, id, enabled);
            break;
          }
          // c3/data/segments/evaluateOnlyIfUsed?id=<segmentId>&enable=<true|false>
          case evaluateOnlyIfUsed:
          {
            String id = req.getParameter("id");
            String flag = req.getParameter("enable");
            ResponseMessage msg = null;
            if (id == null || id.isEmpty() || flag == null || flag.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'id' or 'enable'.");
              resp.getWriter().print(msg.toString());
              return;
            }
            id = "segment+" + id;
            boolean enabled = false;
            try
            {
              enabled = Boolean.parseBoolean(flag);
            }
            catch (Exception e)
            {
              uctx.getLogger(getClass())
                  .warning(String.format(
                      "Failed to parse value of parameter 'enable'. Reason: %s",
                      e.toString()));
            }

            if (enabled)
            {
              Segment.enableEvaluateOnlyIfUsed(uctx, id);
            }
            else
            {
              Segment.disableEvaluateOnlyIfUsed(uctx, id);
            }
            break;
          }
          // c3/data/segments/convertToUpload?id=<segId>
          case convertToUpload:
          {
            String id = req.getParameter("id");
            id = "segment+" + id;
            ResponseMessage msg = null;
            if (id == null || id.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'id' ");
              resp.getWriter().print(msg.toString());
              return;
            }
            Segment seg = Segment.load(uctx, id);
            SegmentDef sDef = seg.getDef();
            SegmentDef def = new SegmentDef();
            def.setDescription(sDef.getDescription());
            def.setGroup("Uploaded Group");
            def.setName((String) seg.getDefAsMap().get("name"));
            def.setIcon("fa-upload");
            def.setActive(sDef.isActive());

            RuleDef rule = new RuleDef();
            rule.setField(Segment.TYPE_ATTR);
            rule.setOperator("==");
            rule.setValue(Segment.Type.uploaded.name());
            rule.setFunction("");
            def.getRule().add(rule);

            String payload = new JsonMarshaller().serialize(def);
            seg.setPayload(payload);
            seg.save();

            break;
          }
          // c3/data/segments/countStatuses
          case countStatuses:
          {
            List<Segment> segments = new ArrayList<>();
            boolean includeAllUsers = true;
            String payload = ServletUtil.getPayload(req);

            if (payload == null || payload.isEmpty())
            {
              segments = Segment.loadAll(uctx, true);
            }
            else
            {
              List<String> segIds = new JsonMarshaller().readAsObject(payload,
                  List.class);

              segments = segIds.stream()
                  .filter(id -> !id.equals(Const.ALL_USERS))
                  .map(id -> Segment.load(uctx, id, true))
                  .collect(Collectors.toList());

              if (!segIds.contains(Const.ALL_USERS))
              {
                includeAllUsers = false;
              }
            }

            List<Map<String, Object>> ret = new ArrayList<>();

            if (segments == null || segments.isEmpty())
            {
              // Check if request to include total population for NS
              if (includeAllUsers)
              {
                // Query segment info.
                Map<String, Object> segMetric = _getUniqueSegmentInfo(uctx,
                    Const.ALL_USERS);

                Map<String, Object> result = getSegmentInfoForUI(uctx,
                    segMetric, Const.ALL_USERS, false);

                ret.add(result);
              }

              resp.getWriter().print(new JsonMarshaller().serialize(ret));
              return;
            }

            String countIdPrefix = uctx.getNamespace(); // count all ID

            // Query segment info all at once.
            List<Map<String, Object>> allSegmentsMetric = _getAllSegmentInfo(
                uctx);

            if (includeAllUsers)
            {
              // Find metric for ALL_USERS
              Map<String, Object> allUserMetric = allSegmentsMetric.stream()
                  .filter(res -> res.get(Axis.segmentId.name())
                      .equals(Const.ALL_USERS))
                  .findAny().orElse(null);

              if (allUserMetric != null)
              {
                Map<String, Object> status = getSegmentInfoForUI(uctx,
                    allUserMetric, Const.ALL_USERS, false);
                ret.add(status);
              }
            }

            for (Segment seg : segments)
            {
              if (seg == null) continue;
              String sId = seg.getId();

              // Find map of metric for matching segId
              Map<String, Object> segMetric = allSegmentsMetric.stream()
                  .filter(res -> res.get(Axis.segmentId.name()).equals(sId))
                  .findAny().orElse(null);

              if (segMetric == null) continue;

              boolean isUpload = z1.commons.Utils.isUploadedSegment(seg);

              Map<String, Object> status = getSegmentInfoForUI(uctx, segMetric,
                  sId, isUpload);

              ret.add(status);
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;
          }
          // c3/data/segments/ids
          case ids:
          {
            String reqPl = ServletUtil.getPayload(req);
            if (reqPl == null || reqPl.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Missing request payload.");
              resp.getWriter().print(msg.toString());
              return;
            }

            JsonMarshaller jm = new JsonMarshaller();
            List<String> sids = jm.readAsObject(reqPl, List.class);
            List<Map<String, Object>> ret = new ArrayList<>();
            sids.forEach(sid -> {
              if (sid.equalsIgnoreCase("system:allUsers"))
              {
                Map<String, Object> map = new java.util.HashMap<>();
                map.put("id", Const.ALL_USERS);
                map.put("name", "All Users");
                map.put("icon", "fa-users");
                ret.add(map);
              }
              else
              {
                // get the goal payload
                Segment s = Segment.load(uctx, sid, true);
                if (s != null)
                {
                  ret.add(_getSegmentInfo(s));
                }
              }
            });

            resp.getWriter().print(jm.serialize(ret));

            break;
          }

        }

        if (invalidateCache)
        {
          App.notifyClearCache(uctx.getNamespace(), z1.c3.Segment.PREFIX);
        }
      }
    };
  }

  /**
   * Returns map of segment metrics as required by UI.
   * 
   * @param uctx
   * @param segMetric
   * @param segId
   * @param isUpload
   * @return
   */
  private Map<String, Object> getSegmentInfoForUI(UContext uctx,
      Map<String, Object> segMetric, String segId, boolean isUpload)
  {
    Map<String, Object> ret = new HashMap<>();

    if (segMetric == null || segMetric.isEmpty())
    {
      ret.put("id", segId);
      ret.put("totalSoFar", "0");

      if (!isUpload)
      {
        ret.put("lastRunStart", -1L);
        ret.put("lastRunEnd", -1L);
      }

      return ret;
    }

    long totalSoFar = segMetric.containsKey(MetricName.totalPopulation.name())
        ? (long) segMetric.get(MetricName.totalPopulation.name())
        : 0;

    ret.put("id", segId);
    ret.put("totalSoFar", (totalSoFar < 0) ? "?" : totalSoFar);

    if (!isUpload)
    {
      Number lastRunStart = (Number) segMetric
          .get(MetricName.lastCountTime.name());
      Number lastRunDuration = (Number) segMetric
          .get(MetricName.lastCountDuration.name());

      // Last Run Start has to be a large number (millisec)
      if (lastRunStart == null || lastRunStart.longValue() <= 0)
      {
        lastRunStart = -1L;
      }

      // Calculate lastRunEnd only if start and duration is available.
      Number lastRunEnd = -1L;
      if (lastRunStart.longValue() > 0 && lastRunDuration != null)
      {
        lastRunEnd = lastRunStart.longValue() + lastRunDuration.longValue();
      }

      ret.put("lastRunStart", lastRunStart);
      ret.put("lastRunEnd", lastRunEnd);

      String ns = uctx.getNamespace();
      String countId = segId.equals(Const.ALL_USERS) ? ns : ns + "_" + segId;
      ret.putAll(RunStatus.getStatus(uctx, QueryType.segment, countId));
    }

    return ret;
  }

  /**
   * Returns list of segment metrics info in tabular form for all segments.
   *
   * @param uctx
   * @return
   */
  private List<Map<String, Object>> _getAllSegmentInfo(UContext uctx)
  {
    return _getSegmentInfo(uctx, null);
  }

  /**
   * Returns map of segment metrics in tabular form for specific segmentId.
   *
   * @param uctx
   * @param segmentId
   * @return
   */
  private Map<String, Object> _getUniqueSegmentInfo(UContext uctx,
      String segmentId)
  {
    List<Map<String, Object>> segmentMetric = _getSegmentInfo(uctx, segmentId);

    // If metric info was found list will contain exactly 1 item.
    return segmentMetric != null && !segmentMetric.isEmpty()
        ? segmentMetric.get(0)
        : null;
  }

  /**
   * Returns segment metrics info in tabular form for specific segmentId or all
   * segments if {@param segmentId} is null. [ { "segmentId": ...,
   * "totalPopulation": ..., "lastCountDuration": ..., "lastCountTime": ... },
   * ... ]
   *
   * @param uctx
   * @param segmentId
   * @return
   */
  private List<Map<String, Object>> _getSegmentInfo(UContext uctx,
      String segmentId)
  {
    // Build query payload.
    List<Map<String, Object>> qpayload = new ArrayList<>();
    final List<Map<String, Object>> filter = new ArrayList<>(1);

    // Filter if segmentId not null
    if (segmentId != null && !segmentId.isEmpty())
    {
      Map<String, Object> f = new HashMap<>();
      f.put("attr", Axis.segmentId.name());
      f.put("op", "=");
      f.put("val", segmentId);
      filter.add(f);
    }

    metricsToQuery.forEach(metric -> {
      Map<String, Object> m = new HashMap<>();
      m.putAll(commonparams);
      m.put("filter", filter);
      m.put("metric", metric);
      qpayload.add(m);
    });

    String qryStr = new JsonMarshaller().serialize(qpayload);
    AnalyticQueryDef qDef = new AnalyticQueryBuilder().loadFromPayload(qryStr)
        .getQuery();

    // Execute query
    List<Map<String, Object>> qResult = new QueryOptimizer(uctx, true)
        .addAnalyticQuery(qDef).execute();

    List<String> groupBy = qDef.getMetricQuery().get(0).getGroupBy();

    // Return joined result
    return AnalyticsUtils.joinResultOnGroupBy(qResult, groupBy);
  }

  /**
   * 
   * @param ctx
   * @param sIds
   * @param recountAll
   * 
   * @return
   */
  private ResponseMessage _recountSegments(final UContext ctx,
      List<String> sIds, boolean recountAll)
  {
    ULogger logger = ctx.getLogger(getClass());
    ResponseMessage msg = null;

    if (sIds == null || sIds.isEmpty())
    {
      if (logger.canLog())
      {
        logger.log("No segment to recount.");
      }
      msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.segmentRecountFailed, "No segment to recount.");
      return msg;
    }

    // /-- Request to count all segments at once
    if (recountAll)
    {
      // Check if another recount all is in progress
      if (RunStatus.isRunning(ctx, QueryType.segment, ctx.getNamespace()))
      {
        if (logger.canLog())
        {
          logger.log(
              "Counting all segments is in progress. Please wait until finished before recount all again.");
        }
        msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
            ResponseMessage.Type.segmentRecountFailed,
            "Counting all segments is in progress. Please wait until finished before recount all again.");
        return msg;
      }

      // Check if any individual segment recount is in progress
      List<Segment> allSegments = Segment.loadAll(ctx, true);
      if (allSegments != null && !allSegments.isEmpty())
      {
        for (Segment seg : allSegments)
        {
          if (seg == null || Utils.isUploadedSegment(seg)) continue;
          String sId = seg.getId();
          String sName = seg.getDef().getName();
          if (RunStatus.isRunning(ctx, QueryType.segment,
              ctx.getNamespace() + "_" + sId))
          {
            if (logger.canLog())
            {
              logger.log("Segment '" + sName
                  + "' counting is in progress. Please wait until finished before recount all again.");
            }
            msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.segmentRecountFailed, "Segment '" + sName
                    + "' counting is in progress. Please wait until finished before recount all again.");
            return msg;
          }
        }
      }

      // Check if any SI is running
      if (_anyC1IsRunning(ctx))
      {
        if (logger.canLog())
        {
          logger.log(
              "One or more Schedule Interactions is running. Please rerun segment counting later.");
        }

        msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
            ResponseMessage.Type.segmentRecountFailed,
            "One or more Schedule Interactions is running. Please rerun segment counting later.");
        return msg;
      }
    }
    // /--- Case: request to count individual segment
    else
    {
      // Check if a recount all is in progress
      if (RunStatus.isRunning(ctx, QueryType.segment, ctx.getNamespace()))
      {
        if (logger.canLog())
        {
          logger.log(
              "Counting all segments is in progress. Please wait until finished before recount this segment.");
        }
        msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
            ResponseMessage.Type.segmentRecountFailed,
            "Counting all segments is in progress. Please wait until finished before recount this segment.");
        return msg;
      }

      for (String sId : sIds)
      {
        Segment s = Segment.load(ctx, sId, true);
        String sName = s.getDef().getName();
        // Check if this segment count is in progress
        if (RunStatus.isRunning(ctx, QueryType.segment,
            ctx.getNamespace() + "_" + sId))
        {
          if (logger.canLog())
          {
            logger.log("Counting segment '" + sName + "' is in progress. "
                + "Please wait until finished before recount this segment again.");
          }
          msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
              ResponseMessage.Type.segmentRecountFailed,
              "Counting segment '" + sName + "' is in progress. "
                  + "Please wait until finished before recount this segment again.");
          return msg;
        }

        // check if any C1 using this segment is currently running
        if (_anyC1UsingSegmentIsRunning(ctx, sId))
        {
          if (logger.canLog())
          {
            logger.log("One or more Scheduled Interaction using segment '"
                + sName
                + "' is running.  Please wait until finished before recount this segment.");
          }
          msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
              ResponseMessage.Type.segmentRecountFailed,
              "One or more Scheduled Interaction using segment '" + sName
                  + "' is running.  Please wait until finished before recount this segment.");
          return msg;
        }
      }
    }

    String message = (recountAll) ? "Starting recount for all segments."
        : "Starting recount for segments: " + sIds;

    if (logger.canLog())
    {
      logger.log(message);
    }

    msg = new ResponseMessage(ctx, ResponseMessage.Status.processing,
        ResponseMessage.Type.segmentRecountProcessing, message
            + ". Please note that Schedule Interaction using any segments under counting will not be running when recount is in progress.");

    if (recountAll)
    {
      RunStatus.prepareOperation(RunStatus.QueryType.segment,
          ctx.getNamespace(), true);
    }
    else
    {
      sIds.forEach(sId -> {
        RunStatus.prepareOperation(RunStatus.QueryType.segment,
            ctx.getNamespace() + "_" + sId, true);
      });
    }

    // invoke async job to populate segment count accordingly
    JobQueue jQ = JobQueue.getInstance("SegmentPopulationExec",
        z1.c3.segment.SegmentCountExec.class);

    Job job = jQ.createJob("SegmentPopulationExec", ctx);
    Map<String, Object> map = new HashMap<>();
    map.put("sIds", sIds);
    map.put("_recountall", (recountAll) ? "true" : "false");
    String pay = new JsonMarshaller().serialize(map);
    job.setPayload(pay);
    job.setPriority(Priority.assignment);
    try
    {
      jQ.submit(job);
    }
    catch (Exception e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }

    return msg;
  }

  /**
   * @param uctx
   * @return
   */
  private boolean _anyC1IsRunning(UContext uctx)
  {
    // Check if any SI is running
    List<Journey> journeys = Journey.forceLoadAll(uctx, Type.c1);
    for (Journey j : journeys)
    {
      if (j == null || !RunStatus.isRunning(uctx, QueryType.otc, j.getId()))
      {
        continue;
      }
      return true;
    }
    return false;
  }

  /**
   * @param uctx
   * @param sId
   * @return
   */
  private boolean _anyC1UsingSegmentIsRunning(UContext uctx, String sId)
  {
    // Check if any SI is running
    List<Journey> journeys = Journey.forceLoadAll(uctx, Type.c1);
    for (Journey j : journeys)
    {
      if (j == null || !RunStatus.isRunning(uctx, QueryType.otc, j.getId()))
      {
        continue;
      }
      JourneyDef jDef = j.getDef();
      List<StateDef> states = jDef.getStates();
      for (StateDef state : states)
      {
        if (!state.getRef().equals(sId))
        {
          continue;
        }
        return true;
      }
    }
    return false;
  }

  /**
   * Gets the references.
   *
   * @param ctx
   *          the ctx
   * @param sid
   *          the sid
   * @return the list
   */
  private List<Object> _getReferences(UContext ctx, String sid)
  {
    String id = sid;
    List<Object> refs = _getSegmentReferences(ctx, Journey.Type.journey, id);
    refs.addAll(_getSegmentReferences(ctx, Journey.Type.campaign, id));
    refs.addAll(_getSegmentReferences(ctx, Journey.Type.c1, id));
    return refs;
  }

  /**
   * Checks if is signal ref found.
   *
   * @param ctx
   *          the ctx
   * @param jType
   *          the j type
   * @param id
   *          the id
   * @return the list
   */
  private List<Object> _getSegmentReferences(UContext ctx, Journey.Type jType,
      String id)
  {
    List<Journey> jList = Journey.forceLoadAll(ctx, jType);

    List<Object> journeyRefs = new ArrayList<>();
    List<Journey> jRefs = new ArrayList<>();
    List<StateDef> stateDefs = null;
    for (Journey j : jList)
    {
      if (j == null) continue;
      stateDefs = j.getDef().getStates();
      for (StateDef stateDef : stateDefs)
      {
        if (null != stateDef.getRef() && Stream.of(stateDef.getRef().split(","))
            .anyMatch(s -> s.equals(id)))
        {
          jRefs.add(j);
          break;
        }
      }
    }
    if (!jRefs.isEmpty())
    {
      _setRefMap(jRefs, journeyRefs, jType);
    }

    return journeyRefs;
  }

  /**
   * Sets the ref map.
   *
   * @param jRefs
   *          the j refs
   * @param journeyRefs
   *          the journey refs
   * @param jType
   *          the j type
   */
  private void _setRefMap(List<Journey> jRefs, List<Object> journeyRefs,
      Journey.Type jType)
  {
    jRefs.forEach(j -> {
      Map<String, Object> refMap = new HashMap<>();
      refMap.put("id", j.getId());
      refMap.put("name", j.getName());
      refMap.put("type", jType.name());
      journeyRefs.add(refMap);
    });
  }

  /**
   * Updates upload process status for segment upload.
   * 
   * @param uctx
   * @param segmentId
   * @param opType
   * 
   * @return
   * @throws InterruptedException
   * 
   */
  private Map<String, Object> _updateUploadSegmentStatus(final UContext uctx,
      String segmentId, OpType opType) throws InterruptedException
  {
    Map<String, Object> ret = new HashMap<>();
    List<Map<String, Object>> payload = new ArrayList<>();

    if (null != segmentId)
    {
      SegmentLoaderRecorder sr = new SegmentLoaderRecorder(uctx);
      List<Map<String, Object>> stats = sr.getSegmentLoaderStat(segmentId);
      if (null != stats && !stats.isEmpty())
      {
        stats.forEach(stat -> {
          if (null != stat && !stat.isEmpty())
          {
            int segTotalCount = stat.containsKey("total")
                ? ((Number) stat.get("total")).intValue()
                : 0;
            int successCount = stat.containsKey("success")
                ? ((Number) stat.get("success")).intValue()
                : 0;
            int rowCount = stat.containsKey("row")
                ? ((Number) stat.get("row")).intValue()
                : 0;
            int removeCount = stat.containsKey("remove")
                ? ((Number) stat.get("remove")).intValue()
                : 0;
            int skipCount = stat.containsKey("skip")
                ? ((Number) stat.get("skip")).intValue()
                : 0;
            int syntaxCount = stat.containsKey("syntax")
                ? ((Number) stat.get("syntax")).intValue()
                : 0;
            int idNotFoundCount = stat.containsKey("notFound")
                ? ((Number) stat.get("notFound")).intValue()
                : 0;
            String startTime = stat.containsKey("startTime")
                ? stat.get("startTime").toString()
                : "";
            String endTime = stat.containsKey("endTime")
                ? stat.get("endTime").toString()
                : "";
            // for backward compatibility
            if (rowCount == 0)
            {
              rowCount = successCount;
            }
            String fileName = (String) stat.get("fileName");
            String timestamp = (String) stat.get("timestamp");
            List<String> files = new ArrayList<>();
            files.add(fileName);
            int processedCount = rowCount + syntaxCount;
            String summary = successCount + " profiles added.";
            Map<String, Object> info = null;
            info = new HashMap<>();
            info.put("total", segTotalCount);
            info.put("success", rowCount);
            info.put("add", successCount);
            info.put("remove", removeCount);
            info.put("skip", skipCount);
            info.put("row", rowCount);
            info.put("syntax", syntaxCount);
            info.put("notfound", idNotFoundCount);
            info.put("fileName", fileName);
            info.put("segid", segmentId);
            info.put("startTime", startTime);
            info.put("endTime", endTime);
            if (timestamp != null)
            {
              info.put("timestamp", timestamp);
            }
            if (syntaxCount > 0)
            {
              info.put("status", Status.fail.getStatus());
            }
            else if (processedCount < segTotalCount)
            {
              summary = (removeCount == 0) ? summary
                  : summary + " " + removeCount + " profiles removed. ";
              summary = (idNotFoundCount == 0) ? summary
                  : summary + " Found " + idNotFoundCount
                      + " IDs that do not exist in the system. ";
              summary = (skipCount == 0) ? summary
                  : summary + " " + skipCount + " IDs skipped. ";
              summary = (syntaxCount == 0) ? summary
                  : summary + "and " + syntaxCount + " rows with syntax errors";
              info.put("status", Status.processing.getStatus());
            }
            else
            {
              if (rowCount == 0)
              {
                summary = "Unable to update any profile records. No keys matched or no record found.";
                info.put("status", Status.fail.getStatus());
              }
              else
              {
                summary = (removeCount == 0) ? summary
                    : summary + " " + removeCount + " profiles removed. ";
                summary = (idNotFoundCount == 0) ? summary
                    : summary + " Found " + idNotFoundCount
                        + " IDs that do not exist in the system. ";
                summary = (skipCount == 0) ? summary
                    : summary + " " + skipCount + " IDs skipped. ";
                summary = (syntaxCount == 0) ? summary
                    : summary + "and " + syntaxCount
                        + " rows with syntax errors";
                if (skipCount == 0 && syntaxCount == 0 && idNotFoundCount == 0)
                {
                  info.put("status", Status.finished.getStatus());
                }
                else
                  info.put("status", Status.finishedWithErrors.getStatus());
              }
            }
            info.put("message", summary);
            // converting payload into json to make output consistent for cube
            // and
            // mongo
            payload.add(info);
          }
        });
      }
      ret.put("stats", payload);
      ret.put("id", segmentId);

    }
    ret.put("errors", _getStatsFromMongo(uctx, segmentId, opType));
    return ret;
  }

  /**
   * Gets the stats from mongo.
   *
   * @param uctx
   *          the uctx
   * @param segmentName
   *          the segment name
   * @param opType
   *          the op type
   * @return the map
   */
  @SuppressWarnings("unchecked")
  private Map<String, List<Map<String, Object>>> _getStatsFromMongo(
      final UContext uctx, String segmentName, OpType opType)
  {
    JsonMarshaller jm = new JsonMarshaller();

    // reading stats from mongo
    List<Map<String, Object>> mongoStats = FileLoaderStats.getStats(uctx,
        segmentName, opType);
    Map<String, List<Map<String, Object>>> result = new HashMap<>();
    Map<String, Map<String, Object>> resultWithoutTimestamp = new HashMap<>();
    // collecting stats group by fileName
    for (Map<String, Object> stat : mongoStats)
    {
      final Map<String, Object> payLoad = jm
          .readAsMap((String) stat.get("payload"));
      // in upload case fileName list size is 1
      final String fileName = ((List<String>) (payLoad).get("fileName")).get(0);
      if (!result.isEmpty() && (result.containsKey(fileName)))
      {
        // If a timestamp is found in the stat payload, add the reason info
        // by filename and timestamp.
        final String timestamp = (String) payLoad.get("timestamp");
        if (timestamp != null)
        {
          List<Map<String, Object>> reasonsList = Optional
              .ofNullable(result.get(fileName))
              .orElseGet(ArrayList<Map<String, Object>>::new);

          Map<String, Object> map = new HashMap<>();
          map.put("timestamp", timestamp);
          map.put("reasons", payLoad.get("infos"));

          reasonsList.add(map);
          result.putIfAbsent(fileName, reasonsList);
        }
        else
        {
          // No timestamp was found in the mongo payload so add reason
          // info by filename only.
          Map<String, Object> filenameMap = (resultWithoutTimestamp
              .get(fileName));
          List<Map<String, Object>> reasonsList = Optional
              .ofNullable(
                  (List<Map<String, Object>>) filenameMap.get("reasons"))
              .orElseGet(ArrayList<Map<String, Object>>::new);

          reasonsList.addAll((List<Map<String, Object>>) payLoad.get("infos"));
          filenameMap.putIfAbsent("reasons", reasonsList);
        }
      }
      else
      {
        final String timestamp = (String) payLoad.get("timestamp");
        if (timestamp != null)
        {
          // If a timestamp is found in the payload, add the reason info
          // by filename and timestamp.
          Map<String, Object> map = new HashMap<>();
          map.put("timestamp", timestamp);
          map.put("reasons", payLoad.get("infos"));

          List<Map<String, Object>> reasonsList = new ArrayList<>();
          reasonsList.add(map);
          result.put(fileName, reasonsList);
        }
        else
        {
          Map<String, Object> map = new HashMap<>();
          map.put("reasons", payLoad.get("infos"));
          resultWithoutTimestamp.put(fileName, map);
        }
      }
    }

    // Merge stats without timestamps into the results
    resultWithoutTimestamp.forEach((filename, values) -> {
      List<Map<String, Object>> reasonsList = Optional
          .ofNullable(result.get(filename))
          .orElseGet(ArrayList<Map<String, Object>>::new);
      reasonsList.add(values);
      result.putIfAbsent(filename, reasonsList);
    });
    return result;
  }

}
