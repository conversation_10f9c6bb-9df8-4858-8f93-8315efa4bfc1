package com.z1.handler;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.analytics.postprocessors.StatisticalSignificanceConversion;
import com.z1.handler.api.Const;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.data.Result;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.JsonRestSerializer;
import udichi.core.util.ServletUtil;
import udichi.gateway.apiservice.PipelineExecutor;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.actions.AbstractMobileAction;
import z1.actions.AbstractMobileAction.ActionDeliveryStatus;
import z1.actions.ActionUtils;
import z1.actions.test.cg.ControlGroupRecorder;
import z1.actions.test.manual.ManualActionRecorder;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.ExpChangeLog;
import z1.c3.Journey;
import z1.c3.Journey.ExeMode;
import z1.c3.Journey.JourneySummaryInfo;
import z1.c3.Journey.Type;
import z1.c3.Segment;
import z1.c3.api.Commons.ReqFields;
import z1.c3.def.ActivitySelectorDef;
import z1.c3.def.JourneyDef;
import z1.c3.def.StateDef;
import z1.c3.def.TimerDef;
import z1.channel.ChannelDefWrapper;
import z1.commons.RunStatus;
import z1.commons.RunStatus.QueryType;
import z1.commons.Utils;
import z1.commons.def.ActionDef;
import z1.commons.def.ParamDef;
import z1.core.ListOfProperties;
import z1.core.runstatus.RunStatusConst.RunCommand;
import z1.core.runstatus.RunStatusRequester;
import z1.core.utils.TimeUtils;
import z1.expression.ScriptObject;
import z1.kb.KBDocumentRetreiver;
import z1.kb.KBResponse;
import z1.processing.InteractionEvaluator;
import z1.processing.InteractionEvaluator.TimerUnit;
import z1.processing.JourneyRuntimeUtils;
import z1.pubwf.UserComment;
import z1.pubwf.UserTask;
import z1.stats.ActionAckStatRecorder;
import z1.stats.ActionStateMetrics;
import z1.stats.ActionsTargetedMetrics;
import z1.stats.ExperienceDailyStatsRecorder;
import z1.stats.ExperienceDailyStatsRecorder.ActionStatus;
import z1.stats.ExperienceTotalStatsRecorder;
import z1.stats.JourneySegmentIntersectionStatsRecorder;
import z1.stats.JourneySegmentStatsRecorder;
import z1.stats.SegmentStatsRecorder;
import z1.stats.UsersTargetedStatsRecorder;
import z1.template.JourneyDesignUtils;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateConst.FilterOption;
import z1.template.TemplateUtils;
import z1.users.AccessControl;
import z1.users.User;

public class JourneyHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    stats,
    activeStats,
    segmentStat,
    messageStat,
    interactionStat,
    actionStat,
    timePayload,
    id,
    insight,
    comment,
    info, // Returns one record similar to "all" response
    expChangeLog,
    query,
    metaInfo,
    dailyInteraction,
    dailyConversion,
    runStatus,
    campaignsTriggeredByEvent,
    overviewStats, // Including c1 expired with day range
    pauseExpired,
    statisticalSignificance
  }

  // Supported post commands
  private enum PostCommand
  {
    validate,
    create,
    update,
    ready, // Puts in ready state but not published yet
    updateTimeInterval,
    delete,
    suspend,
    resume,
    publish,
    execmode,
    unpublish,
    edit,
    comment,
    runCampaignOnce,
    stop,
    park;
  } 

  // Segment stats
  private enum SegmentStat
  {
    index,
    name,
    userCount,
    avgUserCount,
    conversion,
    conversionPercent,
    conversionCountWithAction,
    conversionCountNoAction;
  }

  private Type _journeyType = Type.journey;

  public JourneyHandler(Type journeyType)
  {
    this._journeyType = journeyType;
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          command = GetCommand.id;
        }

        if (!checkIfEnableSE(ctx, resp))
        {
          return;
        }

        switch (command)
        {
          // c3/data/journey/all => Sends all journeys
          case all:
          {
            String[] subParts = new String[2];
            String type = _journeyType.name();
            
            req.setAttribute("id", req.getParameter("jid"));
            req.setAttribute("type", type);
            req.setAttribute("hasStatus", "false");
            subParts[0] = type;
            subParts[1] = "allInstances";
            new BXHandler().post().handle(ctx, subParts, req, resp);

            break;
          }
          
          //c3/data/journey/activeStats?startDate=<startDate>&endDate=<endDate>
          case activeStats:
          {

            if (z1.commons.Utils.isMarketPlace) return;

            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

            Map<String, Object> result = new HashMap<>(10);

            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            String filter = FilterOption.active.name();
           
            if (startDate == null || endDate == null)
            {
              TimeUtils timeUtils = new TimeUtils();
              endDate = timeUtils.getDate();
              startDate = timeUtils.getPreviousDate(7);
            }
            try
            {
              List<Journey> journeys = Journey.loadJourneysForExecution(ctx,
                  Type.campaign);
              if (!journeys.isEmpty())
              {
                result.put("activeCampaign", journeys.size());

                for (Journey j : journeys)
                {
                  try
                  {
                    // We won't send the hidden journey names
                    if (j == null || j.isHidden()
                        || j.getId()
                            .contains("udc.system.core.NamespaceExperience:"))
                      continue;

                    long actionCount = ExperienceDailyStatsRecorder
                        .getTotalActionsForRange(ctx, j.getId(), startDate,
                            endDate, ExeMode.live, ActionStatus.sentDelta);
                    Map<String, Object> defItemMap = j.getDefItem().getValues();
                    Map<String, Object> pl2map = j.getPayload2();
                   
                    Long creationTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.timestamp.name());
                    Long lastUpdatedTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.lastUpdated.name());

                    Map<String, Object> map = new HashMap<>(10);
                    if (pl2map != null && (!pl2map.isEmpty()))
                    {
                      if (pl2map.get("artifactId") != null)
                      {
                        String artId = (String) pl2map.get("artifactId");
                        if (artId != null && (!artId.isEmpty())) map.put("isTemplate", true);
                      }
                    }
                    map.put(JourneySummaryInfo.id.name(), j.getId());
                    map.put(JourneySummaryInfo.name.name(), j.getName());

                    map.put(JourneySummaryInfo.description.name(),
                        j.getDescription());
                    map.put(JourneySummaryInfo.aSent.name(), actionCount);
                    map.put(JourneySummaryInfo.creationTime.name(),
                        creationTime);
                    map.put(JourneySummaryInfo.lastUpdatedTime.name(),
                        lastUpdatedTime);
                    map.put(JourneySummaryInfo.type.name(),
                        Type.campaign);
                    if (Type.campaign.equals(j.getType()))
                    {
                      map.put(JourneySummaryInfo.timePayload.name(), j.getTimePayload());
                      String state = (String) defItemMap
                            .get(DefinitionItem.Fields.state.name());
                      map.put(JourneySummaryInfo.state.name(), state);
                    }
                    
                    Map<String, Set<String>> actionTypeMap = JourneyDesignUtils.getUsedActionSTypesAndNames(j);
                    map.put(JourneySummaryInfo.actionTypes.name(),actionTypeMap.keySet());
                    map.put("actionNames",actionTypeMap);
                    
                    // add action count and push action count to map
                    _populateActionCount(j, map);
                    if (null != map)
                    {
                      ret.add(map);
                    }
                  }
                  catch (Throwable e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                    // Ignore the journey that are rogue and report them to
                    // the
                    // context
                    ctx.getLogger(getClass())
                        .warning(String.format(
                            "Failed to load a journey %s. Reason: %s",
                            j.getName(), e.toString()));
                  }
                }
              }
              Collections.sort(ret,new ActionCountComparator());
              result.put("campaignList", ret);
              ret = new ArrayList<Map<String,Object>>();
              journeys = Journey.loadJourneysForExecution(ctx,
                  Type.c1);
              if (!journeys.isEmpty())
              {
                result.put("activeC1", journeys.size());

                for (Journey j : journeys)
                {
                  try
                  {
                    // We won't send the hidden journey names
                    if (j == null || j.isHidden()
                        || j.getId()
                            .contains("udc.system.core.NamespaceExperience:"))
                      continue;

                    long actionCount = ExperienceDailyStatsRecorder
                        .getTotalActionsForRange(ctx, j.getId(), startDate,
                            endDate, ExeMode.live, ActionStatus.sentDelta);
                    Map<String, Object> defItemMap = j.getDefItem().getValues();
                    Map<String, Object> pl2map = j.getPayload2();
                    Long creationTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.timestamp.name());
                    Long lastUpdatedTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.lastUpdated.name());

                    Map<String, Object> map = new HashMap<>(10);
                    if (pl2map != null && (!pl2map.isEmpty()))
                    {
                      if (pl2map.get("artifactId") != null)
                      {
                        String artId = (String) pl2map.get("artifactId");
                        if (artId != null && (!artId.isEmpty())) map.put("isTemplate", true);
                      }
                    }
                    map.put(JourneySummaryInfo.id.name(), j.getId());
                    map.put(JourneySummaryInfo.name.name(), j.getName());

                    map.put(JourneySummaryInfo.description.name(),
                        j.getDescription());
                    map.put(JourneySummaryInfo.aSent.name(), actionCount);
                    map.put(JourneySummaryInfo.creationTime.name(),
                        creationTime);
                    map.put(JourneySummaryInfo.lastUpdatedTime.name(),
                        lastUpdatedTime);
                    map.put(JourneySummaryInfo.type.name(),
                        Type.c1);
                   
                    Map<String, Set<String>> actionTypeMap = JourneyDesignUtils.getUsedActionSTypesAndNames(j);
                    map.put(JourneySummaryInfo.actionTypes.name(),actionTypeMap.keySet());
                    map.put("actionNames",actionTypeMap);
                    
                    // add action count and push action count to map
                    _populateActionCount(j, map);
                    if (null != map)
                    {
                      ret.add(map);
                    }
                  }
                  catch (Throwable e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                    // Ignore the journey that are rogue and report them to
                    // the
                    // context
                    ctx.getLogger(getClass())
                        .warning(String.format(
                            "Failed to load a journey %s. Reason: %s",
                            j.getName(), e.toString()));
                  }
                }
              }
              Collections.sort(ret,new ActionCountComparator());
              result.put("c1List", ret);
              String payload = new JsonMarshaller().serialize(result);
              resp.getWriter().print(payload);
              return;
            }
            catch (Exception e1)
            {
              Map<String, String> map = new HashMap<>(1);
              map.put("Inavlid", "Inavlid request param : " + filter
                  + ". Please provide valid type");
              resp.getWriter().print(new JsonMarshaller().serialize(map));
            }
            break;
          }
          case stats: 
          {
            if (z1.commons.Utils.isMarketPlace)
              return;
            if (pathParts.length < 2) return;

            String isBU = req.getParameter("isBU");
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);
            List<Journey> journeys = Journey.forceLoadAll(ctx, _journeyType);
            Map<String, Object> channelMap = Journey.getChannels(ctx);
            
            String filter = req.getParameter("filter");
            String days = null;
            if (null != filter)
            {
              if (filter.equals(FilterOption.last5days.name()))
              {
                days = "5";
                filter = null;
              }
            }
            
            if (null != filter)
            {
              try
              {
                FilterOption p = FilterOption.valueOf(filter);

                for (Journey j : journeys)
                {
                  try
                  {
                    // We won't send the hidden journey names
                    if (j == null || j.isHidden() || j.getId().contains("udc.system.core.NamespaceExperience:")) continue;

                    Map<String, Object> map = _getJourneySummaryStat(ctx, j,
                        _journeyType, 0, channelMap, p,isBU);
                    if (null != map)
                    {
                      ret.add(map);
                    }
                  }
                  catch (Throwable e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                    // Ignore the journey that are rogue and report them to
                    // the
                    // context
                    ctx.getLogger(getClass())
                        .warning(String.format(
                            "Failed to load a journey %s. Reason: %s",
                            j.getName(), e.toString()));
                  }
                }

                String payload = new JsonMarshaller().serialize(ret);
                resp.getWriter().print(payload);
                return;
              }
              catch (Exception e1)
              {
                Map<String, String> map = new HashMap<>(1);
                map.put("Inavlid", "Inavlid request param : " + filter
                    + ". Please provide valid type value:active,inactive,draft");
                resp.getWriter().print(new JsonMarshaller().serialize(map));
              }
            }
            else
            {
              long startCreateTime = -1;
              if (days != null)
              {
                int nDays = Integer.parseInt(days);
                if (nDays > 0)
                {
                  long now = Calendar.getInstance().getTimeInMillis();
                  startCreateTime = now - (nDays * 24 * 60 * 60 * 1000);
                }
              }

              for (Journey j : journeys)
              {
                try
                {
                  // We won't send the hidden journey names
                  if (j.isHidden() || j.getId()
                      .contains("udc.system.core.NamespaceExperience:"))
                    continue;

                  Map<String, Object> map = _getJourneySummaryStat(ctx, j,
                      _journeyType, startCreateTime, channelMap,
                      FilterOption.days,isBU);
                  if (null != map)
                  {
                    ret.add(map);
                  }
                }
                catch (Throwable e)
                {
                  z1.commons.Utils.showStackTraceIfEnable(e, null);
                  // Ignore the journey that are rogue and report them to the
                  // context
                  ctx.getLogger(getClass())
                      .warning(String.format(
                          "Failed to load a journey %s. Reason: %s",
                          j.getName(), e.toString()));
                }
              }

              String payload = new JsonMarshaller().serialize(ret);
              resp.getWriter().print(payload);
            }
            break;
          }
          // c3/data/[journey|campaign|c1|campaignonce]/info?journeyId=<journey-id>
          // Returns the same info as the "all" call returns but only for
          // a given journey ID
          case info:
          {
            String journeyId = req.getParameter("journeyId");
            
            //String journeyId = pathParts[1];
            Journey j = Journey.forceLoadDef(ctx, journeyId, _journeyType);
            if (j == null) return;

            try
            {
              Map<String, Object> map = _loadJourneyInfo(ctx, j, -1,_journeyType);
              if (map != null && !map.isEmpty())
              {
                Object createdBy = map.get(JourneySummaryInfo.createdBy.name());
                Object lastUpdatedBy = map.get(JourneySummaryInfo.lastUpdatedBy.name());
                User user = (createdBy != null) ? User.load(ctx, (String) createdBy) : null;
                String createdByName = "Unknown";
                String createdByEmail = "Unknown";

                if (user != null)
                {
                  String fName = Optional
                      .ofNullable((String) user.getValues().get(User.Fields.fname.name()))
                      .orElse("Unknown");
                  String lName = Optional
                      .ofNullable((String) user.getValues().get(User.Fields.lname.name()))
                      .orElse("");
                  createdByEmail = Optional
                      .ofNullable((String) user.getValues().get(User.Fields.email.name()))
                      .orElse(createdBy.toString());
                  createdByName = (lName.isEmpty()) ? fName : fName + " " + lName;
                }
                
                String lastUpdatedByName = "Unknown";
                String lastUpdatedByEmail = "Unknown";
                if (lastUpdatedBy != null)
                {
                  if (createdBy != null && ((String) lastUpdatedBy).equals((String) createdBy))
                  {
                    lastUpdatedByName = createdByName;                    
                  }
                  else
                  {
                    User lastUpdatedByUser = User.load(ctx, (String) lastUpdatedBy);
                    
                    if (lastUpdatedByUser != null)
                    {
                      String fName = Optional
                          .ofNullable((String) lastUpdatedByUser.getValues().get(User.Fields.fname.name()))
                          .orElse("Unknown");
                      String lName = Optional
                          .ofNullable((String) lastUpdatedByUser.getValues().get(User.Fields.lname.name()))
                          .orElse("");
                      lastUpdatedByEmail = Optional
                          .ofNullable((String) lastUpdatedByUser.getValues().get(User.Fields.email.name()))
                          .orElse(lastUpdatedBy.toString());
                      lastUpdatedByName = (lName.isEmpty()) ? fName : fName + " " + lName;
                    }
                  }                                   
                }
                
                map.put(JourneySummaryInfo.createdByName.name(), createdByName);
                map.put(JourneySummaryInfo.createdBy.name(), createdByEmail); // replace id with email
                map.put(JourneySummaryInfo.lastUpdatedByName.name(), lastUpdatedByName);
                map.put(JourneySummaryInfo.lastUpdatedBy.name(), lastUpdatedByEmail); // replace id with email
              }
              if (map == null) return;
              String payload = new JsonMarshaller().serializeMap(map);
              resp.getWriter().print(payload);
            }
            catch (Throwable e)
            {
              z1.commons.Utils.showStackTraceIfEnable(e, null);
              // Ignore the journey that are rogue and report them to the
              // context
              ctx.getLogger(getClass()).warning(
                  String.format("Failed to load a journey %s. Reason: %s",
                      j.getName(), e.toString()));
            }

            return;
          }
          // Obsolute ZMOB-10719
          // c3/data/journey[campaign]/messageStat?journeyId=<journey-id>&stepIndexStr=<step-index>
          // case messageStat:
          // {
          // // This must be a request for a specific journey payload.
          // String journeyId = req.getParameter("journeyId");
          // String stepIndexStr = req.getParameter("stepIndexStr");
          // //String journeyId = pathParts[1];
          // //String stepIndexStr = pathParts[2];
          //
          // // get the journey payload
          // Journey j = Journey.loadDef(ctx, journeyId, _journeyType);
          // if (j == null) return;
          // JourneyDef def = j.getDef();
          // if (def == null) return;
          // String changeDate = req.getParameter("changeDate");
          // String changeInfo = req.getParameter("changeInfo");
          // if (changeDate == null || changeInfo == null)
          // {
          // Map<String, Map<String, String>> changeLog = JourneyUtils
          // .getLatestChangeLog(ctx, journeyId);
          // changeDate = changeLog.keySet().iterator().next();
          // changeInfo = changeLog.get(changeDate).get("changeInfo");
          // }
          // int index = Integer.parseInt(stepIndexStr);
          // Journey.Step step = new Journey.Step(journeyId, index);
          // String stepId = step.getRef();
          // List<Map<String, Object>> stat = _populateMsgStat(ctx, journeyId,
          // stepId,changeDate,changeInfo);
          //
          // String payload = new JsonRestSerializer()
          // .serialize(new JsonMarshaller().serialize(stat));
          // resp.getWriter().print(payload);
          // return;
          // }

          // c3/data/campaign[journey/campaignonce]/interactionStat?journeyId=<journey-id>&stepIndexStr=<step-index>
          case interactionStat:
          {
            // This must be a request for a specific journey payload.
            if (z1.commons.Utils.isMarketPlace)
            {
              return;
            }
            String journeyId = req.getParameter("journeyId");
            String stepIndexStr = req.getParameter("stepIndexStr");
            String changeLogId = req.getParameter("expChangeLogId");
           

            // get the journey payload
            Journey j = Journey.forceLoadDef(ctx, journeyId, _journeyType);
            if (j == null) return;
            JourneyDef def = j.getDef();
            if (def == null) return;
            boolean isMigrated = false;
            // handle change log if changeLogId is 1, then check it is migrated
            ExpChangeLog changeLog = null;
            if (changeLogId == null)
            {
              changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
              changeLogId = changeLog.getId();

              if (changeLogId.equals("1"))
              {
                isMigrated = changeLog.isMigrated();
              }
            }
            else if (changeLogId.equals("1"))
            {
              CustomConfig cc = CustomConfig.load(ctx, journeyId,
                  CustomConfig.Type.expChangeLog);
              if (cc != null)
              {
                JsonMarshaller jm = new JsonMarshaller();
                @SuppressWarnings("unchecked")
                List<ExpChangeLog> expChangeLogs = jm
                    .readAsObject(cc.getPayload(), ArrayList.class);
                if (expChangeLogs != null && !expChangeLogs.isEmpty())
                {
                  changeLog = jm.readAsObject(
                      jm.serialize(expChangeLogs.get(0)), ExpChangeLog.class);

                  isMigrated = changeLog.isMigrated();
                }
              }

            }
            int index = Integer.parseInt(stepIndexStr);
            Journey.Step step = new Journey.Step(journeyId, index);
            String stepId = step.getRef(); 
            List<Map<String, Object>> stat = _populateIntStat(ctx, journeyId,
                stepId, changeLogId, isMigrated);

            String payload = new JsonRestSerializer()
                .serialize(new JsonMarshaller().serialize(stat));
            resp.getWriter().print(payload);
            return;
          }

          // c3/data/campaign[journey/campaignonce]/interactionStat?journeyId=<journey-id>&stepIndexStr=<step-index>
          case dailyInteraction:
          {
            // This must be a request for a specific journey payload.
            if (z1.commons.Utils.isMarketPlace)
            {
              return;
            }
            String journeyId = req.getParameter("journeyId");
            String stepIndexStr = req.getParameter("stepIndexStr");
            String changeLogId = req.getParameter("expChangeLogId");
            
            String f = req.getParameter("f");
            if (f == null) f = "";
            String t = req.getParameter("t");
            if (t == null) t = "";

            // get the journey payload
            Journey j = Journey.forceLoadDef(ctx, journeyId, _journeyType);
            if (j == null) return;
            JourneyDef def = j.getDef();
            if (def == null) return;
            boolean isMigrated = false;
            // handle change log if changeLogId is 1, then check it is migrated
            ExpChangeLog changeLog = null;
            if (changeLogId == null)
            {
              changeLogId = "*";
             
            }
            else
              if (changeLogId != null && changeLogId.equals("1"))
            {
              CustomConfig cc = CustomConfig.load(ctx, journeyId,
                  CustomConfig.Type.expChangeLog);
              if (cc != null)
              {
                JsonMarshaller jm = new JsonMarshaller();
                @SuppressWarnings("unchecked")
                List<ExpChangeLog> expChangeLogs = jm
                    .readAsObject(cc.getPayload(), ArrayList.class);
                if (expChangeLogs != null && !expChangeLogs.isEmpty())
                {
                  changeLog = jm.readAsObject(
                      jm.serialize(expChangeLogs.get(0)), ExpChangeLog.class);

                  isMigrated = changeLog.isMigrated();
                }
              }

            }
            int index = Integer.parseInt(stepIndexStr);
            Journey.Step step = new Journey.Step(journeyId, index);
            String stepId = step.getRef(); 
            List<Map<String, Object>> stat = _populateDailyIntStat(ctx, journeyId,
                stepId, changeLogId, isMigrated, f, t);

            String payload = new JsonRestSerializer()
                .serialize(new JsonMarshaller().serialize(stat));
            resp.getWriter().print(payload);
            return;
          }

          // c3/data/journey/insight?insightType=<insight-type>&cohortType=<cohort-type>[&journeyId=<journey-id>&stepIndexStr=<stepIndexStr>]
          case insight:
          {
            String insightType = req.getParameter("insightType");
            String cohortType = req.getParameter("cohortType");

            if ("cohort".equals(insightType) && "segment".equals(cohortType))
            {
              String journeyId = req.getParameter("journeyId");
              String stepIndexStr = req.getParameter("stepIndexStr");
              String changeLogId = req.getParameter("changeLogId");

              boolean isMigrated = false;

              // handle change log if changeLogId is 1, then check it is
              // migrated
              ExpChangeLog changeLog = null;
              if (changeLogId == null)
              {
                changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
                changeLogId = changeLog.getId();

                if (changeLogId.equals("1"))
                {
                  isMigrated = changeLog.isMigrated();
                }
              }
              else if (changeLogId.equals("1"))
              {
                CustomConfig cc = CustomConfig.load(ctx, journeyId,
                    CustomConfig.Type.expChangeLog);
                if (cc != null)
                {
                  JsonMarshaller jm = new JsonMarshaller();
                  @SuppressWarnings("unchecked")
                  List<ExpChangeLog> expChangeLogs = jm
                      .readAsObject(cc.getPayload(), ArrayList.class);
                  if (expChangeLogs != null && !expChangeLogs.isEmpty())
                  {
                    changeLog = jm.readAsObject(
                        jm.serialize(expChangeLogs.get(0)), ExpChangeLog.class);

                    isMigrated = changeLog.isMigrated();
                  }
                }
              }

              int index = Integer.parseInt(stepIndexStr);
              JourneySegmentIntersectionStatsRecorder s = new JourneySegmentIntersectionStatsRecorder(
                  ctx);
              Map<String, Object> mapEnter = null;
              Map<String, Object> mapConv = null;

              mapEnter = s.getSegmentStats(journeyId, index, "enter",
                  changeLogId, isMigrated);
              mapConv = s.getSegmentStats(journeyId, index, "convert",
                  changeLogId, isMigrated);

              List<Map<String, Object>> stat = _formCohortSegmentInsight(ctx,
                  mapEnter, mapConv);

              String payload = new JsonRestSerializer()
                  .serialize(new JsonMarshaller().serialize(stat));
              resp.getWriter().print(payload);
            }
            return;
          }
          
          case dailyConversion:
          {
            String insightType = req.getParameter("insightType");
            String cohortType = req.getParameter("cohortType");

            String f = req.getParameter("f");
            if (f == null) f = "";
            String t = req.getParameter("t");
            if (t == null) t = "";

            if ("cohort".equals(insightType) && "segment".equals(cohortType))
            {
              String journeyId = req.getParameter("journeyId");
              String stepIndexStr = req.getParameter("stepIndexStr");
              String changeLogId = req.getParameter("changeLogId");

              boolean isMigrated = false;

              // handle change log if changeLogId is 1, then check it is
              // migrated
              ExpChangeLog changeLog = null;
              if (changeLogId == null)
              {
                changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
                changeLogId = changeLog.getId();

                if (changeLogId.equals("1"))
                {
                  isMigrated = changeLog.isMigrated();
                }
              }
              else if (changeLogId != null && changeLogId.equals("1"))
              {
                CustomConfig cc = CustomConfig.load(ctx, journeyId,
                    CustomConfig.Type.expChangeLog);
                if (cc != null)
                {
                  JsonMarshaller jm = new JsonMarshaller();
                  @SuppressWarnings("unchecked")
                  List<ExpChangeLog> expChangeLogs = jm
                      .readAsObject(cc.getPayload(), ArrayList.class);
                  if (expChangeLogs != null && !expChangeLogs.isEmpty())
                  {
                    changeLog = jm.readAsObject(
                        jm.serialize(expChangeLogs.get(0)), ExpChangeLog.class);

                    isMigrated = changeLog.isMigrated();
                  }
                }
              }

              int index = Integer.parseInt(stepIndexStr);
              JourneySegmentIntersectionStatsRecorder s = new JourneySegmentIntersectionStatsRecorder(
                  ctx);

              Map<String, Object> mapEnter = s.getDailySegmentStats(journeyId,
                  index, "enter", changeLogId, isMigrated, f, t);
              Map<String, Object> mapConv = s.getDailySegmentStats(journeyId,
                  index, "convert", changeLogId, isMigrated, f, t);

              List<Map<String, Object>> stat = _formCohortSegmentInsight(ctx,
                  mapEnter, mapConv);

              String payload = new JsonRestSerializer()
                  .serialize(new JsonMarshaller().serialize(stat));
              resp.getWriter().print(payload);
            }
            return;
          }
          
          // c3/data/journey[campaign]/actionStat?journeyId=<journey-id>&stepIndexStr=<step-index>
          case actionStat:
          {
            // This must be a request for a specific journey payload.
            String journeyId = req.getParameter("journeyId");
            String stepIndexStr = req.getParameter("stepIndexStr");
            String changeLogId = req.getParameter("changeLogId");
            String sd = req.getParameter("startDate");
            String ed = req.getParameter("endDate");
            // get the journey payload
            Journey j = Journey.forceLoadDef(ctx, journeyId, _journeyType);
            if (j == null) return;
            boolean isMigrated = false;
            // handle change log if changeLogId is 1, then check it is migrated
            ExpChangeLog changeLog = null;
            if (changeLogId == null)
            {
              changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
              changeLogId = changeLog.getId();
            }
            else  if(changeLogId.equals("*"))
            {
              changeLog = new ExpChangeLog();
              changeLog.setId("*");
            }
            else
            {
              changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId,
                  Integer.parseInt(changeLogId.trim()));
            }
            if (changeLogId.equals("1"))
            {
              isMigrated = changeLog.isMigrated();
            }
            int index = Integer.parseInt(stepIndexStr);
            if (sd == null || ed == null)
            {
              Map<String, Object> stat = _populateActionStatItem(ctx, j, index,
                  changeLog, isMigrated);
              String payload = new JsonRestSerializer()
                  .serialize(new JsonMarshaller().serialize(stat));
              resp.getWriter().print(payload);
            }
            else
            {
              Map<String, Object> stat = _populateActionStatItem(ctx, j, index,
                  changeLog, isMigrated, sd, ed);
              String payload = new JsonRestSerializer()
                  .serialize(new JsonMarshaller().serialize(stat));
              resp.getWriter().print(payload);
            }
            return;
          }

          // c3/data/journey[campaign]/segmentStat?journeyId=<journey-id>
          case segmentStat:
          {
            // This must be a request for a specific journey payload.
            String journeyId = req.getParameter("journeyId");
            // String journeyId = pathParts[1];
            // get the journey payload
            Journey j = Journey.forceLoadDef(ctx, journeyId, _journeyType);
            if (j == null) return;
            JourneyDef def = j.getDef();
            if (def == null) return;
            String changeLogId = req.getParameter("changeLogId");
            boolean isMigrated = false;
            // handle change log if changeLogId is 1, then check it is migrated
            ExpChangeLog changeLog = null;
            if (changeLogId == null)
            {
              changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
              if (changeLog == null) return;
              changeLogId = changeLog.getId();

              if (changeLogId.equals("1"))
              {
                isMigrated = changeLog.isMigrated();
              }
            }
            else if (changeLogId.equals("1"))
            {
              CustomConfig cc = CustomConfig.load(ctx, journeyId,
                  CustomConfig.Type.expChangeLog);
              if (cc != null)
              {
                JsonMarshaller jm = new JsonMarshaller();
                @SuppressWarnings("unchecked")
                List<ExpChangeLog> expChangeLogs = jm
                    .readAsObject(cc.getPayload(), ArrayList.class);
                if (expChangeLogs != null && !expChangeLogs.isEmpty())
                {
                  changeLog = jm.readAsObject(
                      jm.serialize(expChangeLogs.get(0)), ExpChangeLog.class);

                  isMigrated = changeLog.isMigrated();
                }
              }
            }
            List<Map<String, Object>> segmentStatArr = new ArrayList<>();
            String prevSegId = null;
            Long lastUserCount = 0L;
            Long userMoved = 0L;
            int index = 0;
            for (StateDef s : def.getStates())
            {
              Journey.Step step = new Journey.Step(journeyId, index);
              String stepId = step.getRef();

              // FIXME
              // We are loading the segment just to get the name of the segment
              // used here. As the getRef()
              // now can return an array, the next call won't work. We need to
              // introduce a concept of creating
              // name form the segments used.
              String segmentRefStr = s.getRef();
              if (segmentRefStr == null || segmentRefStr.isEmpty()) continue;

              String[] refs = segmentRefStr.split(",");

              // Current user count
              Long userCount = 0L;
              // Total user count
              Long totalUserCount = 0L;
              Long conversion = userMoved;
              double conversionPercent = 0L;
              Long conversionWA = 0L;
              Long conversionNoAction = 0L;

              // We'll compute the overall stat for campaign and journey
              // differently
              if (_journeyType.equals(Type.journey))
              {
                // Journey
                JourneySegmentStatsRecorder jssr = new JourneySegmentStatsRecorder(
                    ctx);
                conversionWA = jssr.getConversionNumbers(journeyId, prevSegId,
                    stepId, changeLogId, isMigrated);

                // Get the added and deleted user counts for the given journey
                // and segment
                List<Map<String, Object>> populationInfos = jssr
                    .getJourneyStatePopulation(journeyId, stepId, changeLogId,
                        isMigrated,false);
                Map<String, Object> populationInfo = !populationInfos.isEmpty()
                    ? populationInfos.get(0)
                    : null;
                Number deleted = populationInfo == null ? 0L
                    : (Number) populationInfo.get("deleted");
                Number added = populationInfo == null ? 0L
                    : (Number) populationInfo.get("added");

                if (deleted == null) deleted = 0L;
                if (added == null) added = 0L;
                userCount = added.longValue() - deleted.longValue();
                if (userCount < 0) userCount = 0L;

                totalUserCount = added.longValue(); // Total count should work
                                                    // in this case
                // //ssr.getAveragePopulation(segId);
                conversionNoAction = jssr.getConversionNumbersWithNoAction(
                    journeyId, prevSegId, stepId, changeLogId, isMigrated,false);

                // Calculate conversion percent
                if (lastUserCount != 0)
                {
                  conversionPercent = Math
                      .round((((double) conversion / lastUserCount) * 100));
                }

                // We'll remeber how many people were here in this bucket to
                // calculate the conversion percent
                lastUserCount = added.longValue();
                userMoved = deleted.longValue();
              }
              else
              {
                // Campaign
                // Get all the refs and compute the total user count
                for (String ref : refs)
                {
                  // Collect the segment stats
                  long l = SegmentStatsRecorder.getTotalPopulation(ctx, ref);
                  if (l > 0) userCount += l;
                }
              }

              String name = null;
              if (refs.length > 0)
              {
                Segment segment = Segment.load(ctx, refs[0], true);
                if (segment != null) name = segment.getDef().getName();
              }

              if (name == null) name = "step" + index;

              Map<String, Object> map = new HashMap<>();
              map.put(SegmentStat.index.name(), index);
              map.put(SegmentStat.name.name(), name);
              map.put(SegmentStat.userCount.name(), userCount);
              map.put(SegmentStat.avgUserCount.name(), totalUserCount);
              map.put(SegmentStat.conversion.name(), conversion);
              map.put(SegmentStat.conversionPercent.name(), conversionPercent);
              map.put(SegmentStat.conversionCountWithAction.name(),
                  conversionWA);
              map.put(SegmentStat.conversionCountNoAction.name(),
                  conversionNoAction);
              segmentStatArr.add(map);

              index++;
              prevSegId = stepId;
            }
            String payload = new JsonMarshaller().serialize(segmentStatArr);
            resp.getWriter().print(payload);
            return;
          }
          
          // c3/data/journey/id?journeyId=<journey-id>
          case id:
          {
            // This must be a request for a specific journey payload.
            String journeyId = req.getParameter("journeyId");
            // get the journey payload
            Journey j = Journey.forceLoadDef(ctx, journeyId, _journeyType);
            if (j == null)
            {
              ctx.getLogger(getClass())
                  .log("Journey not found for this:" + journeyId);
              resp.getWriter().print("{}");
              return;
            }
            Map<String, Object> metaData = Journey.getMetadataFromPayload2(j);
            if (metaData.containsKey("inlineSegmentId"))
            {
              JourneyDef journeyDef = j.getDef();
              journeyDef.getStates().stream().forEach(state -> {
                if (state.getRef() != null)
                {
                  String nonInlineSegmentRef = Arrays
                      .stream(state.getRef().split(",")).map(String::trim)
                      .filter(
                          item -> !item.equals(metaData.get("inlineSegmentId")))
                      .collect(Collectors.joining(","));
                  state.setRef(nonInlineSegmentRef);
                }
              });
            }
            Map<String, Object> jm = new HashMap<>();
            jm.put("payload2", j.getPayload2());
            jm.put("payload", j.getDef());
            jm.put("name", j.getName());
            jm.put("description", j.getDescription());

            resp.getWriter().print(new JsonMarshaller().serialize(jm));

            return;
          }
          
          // c3/data/<journeyType>/metaInfo?journeyId=<journey-id>
          case metaInfo:
          {
            // This must be a request for a specific journey payload.
            String journeyId = req.getParameter("journeyId");

            Map<String, Object> params = new java.util.HashMap<>(10);
            Journey journey = Journey.forceLoadDef(ctx, journeyId, _journeyType);

            if (journey != null)
            {
              params = JourneyRuntimeUtils.getMetaInfo(ctx, journey, _journeyType);
            }
            resp.getWriter().print(new JsonMarshaller().serialize(params));
            return;
          }
          // c3/data/journey/timePayload?journeyId=<journey-id>
          case timePayload:
          {
            // This must be a request for a specific goal with time payload
            // information.
            String journeyId = req.getParameter("journeyId");                                   
            Journey j = Journey.loadDef(ctx, journeyId, _journeyType);

            String timePayload = j.getTimePayload();
            if (timePayload != null)
            {
              resp.getWriter().print(timePayload);
            }

            return;
          }

          // c3/data/journey/comment?journeyId=<journey-id>
          case comment:
          {
            String journeyId = req.getParameter("journeyId");
            
            //String journeyId = pathParts[1];
            Result<UserComment> res = UserComment.loadAll(ctx, journeyId);
            resp.getWriter().print(new JsonMarshaller().serialize(res));
            return;
          }
          case expChangeLog:
          {
            if (z1.commons.Utils.isMarketPlace)
            {
              return;
            }
            String journeyId = req.getParameter("journeyId");
            String expChangeLogId = req.getParameter("changeLogId");
            if (journeyId == null) return;
            CustomConfig cc = CustomConfig.load(ctx, journeyId,
                CustomConfig.Type.expChangeLog);
            if (cc == null) return;
            JsonMarshaller jm = new JsonMarshaller();
            List<ExpChangeLog> changes = jm.readAsObject(cc.getPayload(),
                ArrayList.class);
            if (expChangeLogId == null)
            {
              resp.getWriter().print(new JsonMarshaller().serialize(changes));
              return;
            }
            else
            {
              int changeId = Integer.parseInt(expChangeLogId.trim());
              if (changeId < 1 || changeId > changes.size()) return;
              ExpChangeLog changeLog = jm.readAsObject(
                  jm.serialize(changes.get(changeId - 1)), ExpChangeLog.class);
              resp.getWriter().print(new JsonMarshaller().serialize(changeLog));
              return;
            }
          }
          case query:
          {
            String journeyId = req.getParameter("journeyId");
            String daysParam = req.getParameter("days");
            if (journeyId == null || daysParam == null)
            {
              return;
            }
            int days = Integer.parseInt(daysParam);
            if (days < 1) return;
            long sent = ExperienceDailyStatsRecorder.getCountforLastnDays(ctx,
                journeyId, days, ActionStatus.sent);
            long reached = ExperienceDailyStatsRecorder.getCountforLastnDays(
                ctx, journeyId, days, ActionStatus.reached);
            long notSentCG = ExperienceDailyStatsRecorder.getCountforLastnDays(
                ctx, journeyId, days, ActionStatus.controlGroup);
            long notSent = ExperienceDailyStatsRecorder
                .getCountforLastnDays(ctx, journeyId, days, ActionStatus.other);
            List<Map<String, Object>> notSentReason = ExperienceDailyStatsRecorder
                .getNotSentReasonCountforLastnDays(ctx, journeyId, days);
            Map<String, Object> map = new HashMap<>();
            map.put("sent", sent);
            map.put("reached", reached);
            map.put("controlGroup", notSentCG);
            map.put("notSent", notSent);
            map.put("notSentReason", notSentReason);
            resp.getWriter().print(new JsonMarshaller().serialize(map));
            return;
          }
          case runStatus:
          {
            String journeyId = req.getParameter("journeyId");
            // Call the ManagedJob to fetch the status
            Map<String, Object> status = RunStatusRequester.getInstance()
                .handleCommand(ctx, journeyId, RunCommand.status);
            resp.getWriter().print(new JsonMarshaller().serialize(status));
            return;
          }
          case campaignsTriggeredByEvent:
          {
            if (_journeyType != Type.campaign) return;
            String eventName = req.getParameter("eventName");
            List<Journey> journeys = Journey.forceLoadAll(ctx, Type.campaign);

            List<String> jList = new ArrayList<>();
            for (Journey journey : journeys)
            {
              if (TemplateUtils.isTemplateConfig(journey.getId(), _journeyType.name()))
                continue;
              
              String payload2 = journey.getTimePayload();
              if (payload2 != null && (!payload2.isEmpty()))
              {
                Map<String, Object> pl2 = new JsonMarshaller().readAsMap(payload2);
                if (pl2 != null && (!pl2.isEmpty()) && pl2.get("triggeredBy") != null)
                {
                  List<Map<String, Object>> triggeredBy = (List<Map<String, Object>>) pl2.get("triggeredBy");
                  for(Map<String, Object> tb : triggeredBy)
                  {
                    if (eventName.equals(tb.get("eventName")))
                    {
                      jList.add(journey.getName());
                      break;
                    }
                  }
                }
              }
            }
            resp.getWriter().print(new JsonMarshaller().serialize(jList));
            return;
          }
          //c3/data/journey/overviewStats?startDate=<startDate>&endDate=<endDate>
          case overviewStats:
          {

            if (z1.commons.Utils.isMarketPlace) return;

            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

            Map<String, Object> result = new HashMap<>(10);

            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            String filter = FilterOption.active.name();
           
            if (startDate == null || endDate == null)
            {
              TimeUtils timeUtils = new TimeUtils();
              endDate = timeUtils.getDate();
              startDate = timeUtils.getPreviousDate(7);
            }
            try
            {
              List<Journey> journeys = Journey.loadJourneysForOverview(ctx,
                  Type.campaign, startDate, endDate);
              if (!journeys.isEmpty())
              {
                result.put("activeCampaign", journeys.size());

                for (Journey j : journeys)
                {
                  try
                  {
                    // We won't send the hidden journey names
                    if (j == null || j.isHidden()
                        || j.getId()
                            .contains("udc.system.core.NamespaceExperience:"))
                      continue;

                    long actionCount = ExperienceDailyStatsRecorder
                        .getTotalActionsForRange(ctx, j.getId(), startDate,
                            endDate, ExeMode.live, ActionStatus.sentDelta);
                    Map<String, Object> defItemMap = j.getDefItem().getValues();
                    Map<String, Object> pl2map = j.getPayload2();
                   
                    Long creationTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.timestamp.name());
                    Long lastUpdatedTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.lastUpdated.name());

                    Map<String, Object> map = new HashMap<>(10);
                    if (pl2map != null && (!pl2map.isEmpty()))
                    {
                      if (pl2map.get("artifactId") != null)
                      {
                        String artId = (String) pl2map.get("artifactId");
                        if (artId != null && (!artId.isEmpty())) map.put("isTemplate", true);
                      }
                    }
                    map.put(JourneySummaryInfo.id.name(), j.getId());
                    map.put(JourneySummaryInfo.name.name(), j.getName());

                    map.put(JourneySummaryInfo.description.name(),
                        j.getDescription());
                    map.put(JourneySummaryInfo.aSent.name(), actionCount);
                    map.put(JourneySummaryInfo.creationTime.name(),
                        creationTime);
                    map.put(JourneySummaryInfo.lastUpdatedTime.name(),
                        lastUpdatedTime);
                    map.put(JourneySummaryInfo.type.name(),
                        Type.campaign);
                    if (Type.campaign.equals(j.getType()))
                    {
                      map.put(JourneySummaryInfo.timePayload.name(), j.getTimePayload());
                      String state = (String) defItemMap
                            .get(DefinitionItem.Fields.state.name());
                      map.put(JourneySummaryInfo.state.name(), state);
                    }
                    
                    Map<String, Set<String>> actionTypeMap = JourneyDesignUtils.getUsedActionSTypesAndNames(j);
                    map.put(JourneySummaryInfo.actionTypes.name(),actionTypeMap.keySet());
                    map.put("actionNames",actionTypeMap);
                    
                    // add action count and push action count to map
                    _populateActionCount(j, map);
                    if (null != map)
                    {
                      ret.add(map);
                    }
                  }
                  catch (Throwable e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                    // Ignore the journey that are rogue and report them to
                    // the
                    // context
                    ctx.getLogger(getClass())
                        .warning(String.format(
                            "Failed to load a journey %s. Reason: %s",
                            j.getName(), e.toString()));
                  }
                }
              }
              Collections.sort(ret,new ActionCountComparator());
              result.put("campaignList", ret);
              ret = new ArrayList<Map<String,Object>>();
              journeys = Journey.loadJourneysForOverview(ctx, Type.c1,
                  startDate, endDate);
              if (!journeys.isEmpty())
              {
                result.put("activeC1", journeys.size());

                for (Journey j : journeys)
                {
                  try
                  {
                    // We won't send the hidden journey names
                    if (j == null || j.isHidden()
                        || j.getId()
                            .contains("udc.system.core.NamespaceExperience:"))
                      continue;

                    long actionCount = ExperienceDailyStatsRecorder
                        .getTotalActionsForRange(ctx, j.getId(), startDate,
                            endDate, ExeMode.live, ActionStatus.sentDelta);
                    Map<String, Object> defItemMap = j.getDefItem().getValues();
                    Map<String, Object> pl2map = j.getPayload2();
                    Long creationTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.timestamp.name());
                    Long lastUpdatedTime = (Long) defItemMap
                        .get(DefinitionItem.Fields.lastUpdated.name());

                    Map<String, Object> map = new HashMap<>(10);
                    if (pl2map != null && (!pl2map.isEmpty()))
                    {
                      if (pl2map.get("artifactId") != null)
                      {
                        String artId = (String) pl2map.get("artifactId");
                        if (artId != null && (!artId.isEmpty())) map.put("isTemplate", true);
                      }
                    }
                    map.put(JourneySummaryInfo.id.name(), j.getId());
                    map.put(JourneySummaryInfo.name.name(), j.getName());

                    map.put(JourneySummaryInfo.description.name(),
                        j.getDescription());
                    map.put(JourneySummaryInfo.aSent.name(), actionCount);
                    map.put(JourneySummaryInfo.creationTime.name(),
                        creationTime);
                    map.put(JourneySummaryInfo.lastUpdatedTime.name(),
                        lastUpdatedTime);
                    map.put(JourneySummaryInfo.type.name(),
                        Type.c1);
                   
                    Map<String, Set<String>> actionTypeMap = JourneyDesignUtils.getUsedActionSTypesAndNames(j);
                    map.put(JourneySummaryInfo.actionTypes.name(),actionTypeMap.keySet());
                    map.put("actionNames",actionTypeMap);
                    
                    // add action count and push action count to map
                    _populateActionCount(j, map);
                    if (null != map)
                    {
                      ret.add(map);
                    }
                  }
                  catch (Throwable e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                    // Ignore the journey that are rogue and report them to
                    // the
                    // context
                    ctx.getLogger(getClass())
                        .warning(String.format(
                            "Failed to load a journey %s. Reason: %s",
                            j.getName(), e.toString()));
                  }
                }
              }
              Collections.sort(ret,new ActionCountComparator());
              result.put("c1List", ret);
              String payload = new JsonMarshaller().serialize(result);
              resp.getWriter().print(payload);
              return;
            }
            catch (Exception e1)
            {
              Map<String, String> map = new HashMap<>(1);
              map.put("Inavlid", "Inavlid request param : " + filter
                  + ". Please provide valid type");
              resp.getWriter().print(new JsonMarshaller().serialize(map));
            }
            break;
          }
          //c3/data/campaign/statisticalSignificance?jId=<journey-id>
          case statisticalSignificance:
          {
            try
            {
              String jId = req.getParameter("journeyId");
              if (jId == null || jId.isEmpty())
              {
                ResponseMessage msg = new ResponseMessage(ctx, ResponseMessage.Status.fail);
                resp.getWriter().print(msg.toString());
                return;
              }

              StatisticalSignificanceConversion statisticalSignificanceConversion =
                  new StatisticalSignificanceConversion();
              Map<String, Object> resultPayload = statisticalSignificanceConversion
                  .handleTESummaryRequest(ctx, jId);
              resp.getWriter()
                  .print(new JsonMarshaller().serialize(resultPayload));
              return;
            }
            catch (Exception e)
            {
              ResponseMessage msg = new ResponseMessage(ctx, ResponseMessage.Status.fail, ResponseMessage.Type.serverError, "Could not process Statistical Significance request");
              resp.getWriter().print(msg.toString());
            }
            break;
          }
          // c3/data/journey/pauseExpired
          case pauseExpired:
          {
            List<Journey> pausedExpiredJourneys = JourneyRuntimeUtils
                .pauseExpiredTEs(ctx);
            ResponseMessage responseMessage = new ResponseMessage(ctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                String.format("Successfully paused %s journeys.",
                    pausedExpiredJourneys.size()));
            resp.getWriter().print(responseMessage.toString());
            break;
          }
          default:
          {
            return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        if (!checkIfEnableSE(ctx, resp))
        {
          return;
        }
        boolean invalidateCache = true;
        switch (command)
        {
          // c3/data/journey/validate?isC2=<true|false> => payload has segmentIds and audienceIds
          case validate:
          {
            String p = ServletUtil.getPayload(req);
            boolean isC2 = Boolean.parseBoolean(req.getParameter("isC2"));

            JsonMarshaller jsonMarshaller = new JsonMarshaller();
            Map<String, Object> pMap = jsonMarshaller.readAsMap(p);

            List<String> segmentIds = (List<String>) pMap.get("segmentIds");
            List<String> audienceIds = (List<String>) pMap.get("audienceIds");

            int validationCode = JourneyDesignUtils.validateRules(ctx,
                segmentIds, audienceIds, isC2);

            if (validationCode == 0)
            {
              ResponseMessage responseMessage = new ResponseMessage(ctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Operation '" + command.name() + "' finished!");
              resp.getWriter().print(responseMessage.toString());
              return;
            }

            Map<String, Object> msg = new HashMap<>();
            msg.put("errorCode", validationCode);
            msg.put("errorMsg", JourneyDesignUtils
                .generateFieldConflictErrorMsg(validationCode));

            ResponseMessage responseMessage = new ResponseMessage(ctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                jsonMarshaller.serialize(msg));

            resp.getWriter().print(responseMessage.toString());
            break;
          }
          // c3/data/journey/create?isC2=<true|false> => payload has the definition
          // returns <journey-id>
          case create:
          {
            String jp = ServletUtil.getPayload(req);

            // If no payload is defined, we will simply return
            if (jp == null) return;

            boolean isC2 = Boolean.parseBoolean(req.getParameter("isC2"));
            
            List<String> audienceIds = new ArrayList<>();
            List<String> segmentIds = new ArrayList<>();

            // Parse journey payload and retrieve audienceIds and segmentIds
            JourneyDesignUtils.populateAudienceAndSegmentsFromPayload(ctx, jp,
                audienceIds, segmentIds, isC2);

            int validationCode = JourneyDesignUtils.validateRules(ctx,
                segmentIds, audienceIds, isC2);
            if (validationCode != 0)
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, JourneyDesignUtils
                      .generateFieldConflictErrorMsg(validationCode));
              resp.getWriter().print(msg.toString());
              return;
            }
            
            String role = (String) ctx.getUser().getValues().get(User.ROLE);
            String isBU = req.getParameter("isBU");            
            Journey j = _updateJourney(ctx, null, jp, role, isBU);
            if (j == null) return;

            // Returned the created id
            String jid = j.getId();
            Map<String, Object> map = new java.util.HashMap<>();
            map.put(DefinitionItem.ID, jid);
            resp.getWriter().print(new JsonMarshaller().serializeMap(map));


            // Audit
            ArtifactAudit.newInstance(ctx, ItemTypes.parse(_journeyType.name()),
                jid, j.getName(), Operations.create).save();

            break;
          }
          // c3/data/<journey-type>/update?jid=<journey-id>&isC2=<true|false> , POST body has payload
          case update:
          {
            String jid = req.getParameter("jid");
            String payload = ServletUtil.getPayload(req);
            boolean isC2 = Boolean.parseBoolean(req.getParameter("isC2"));
            List<String> audienceIds = new ArrayList<>();
            List<String> segmentIds = new ArrayList<>();

            // Parse journey payload and retrieve audienceIds and segmentIds
            JourneyDesignUtils.populateAudienceAndSegmentsFromPayload(ctx,
                payload, audienceIds, segmentIds, isC2);

            int validationCode = JourneyDesignUtils.validateRules(ctx,
                segmentIds, audienceIds, isC2);
            if (validationCode != 0)
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, JourneyDesignUtils
                      .generateFieldConflictErrorMsg(validationCode));
              resp.getWriter().print(msg.toString());
              return;
            }

            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            // If journey does not exist, we will simply return
            if (j == null) return;

            j = _updateJourney(ctx, j, payload,null, null);
            if (j == null) return;

            ActionUtils.invalidateScriptCache(ctx, jid);
            
            // Audit
            ArtifactAudit.newInstance(ctx, ItemTypes.parse(_journeyType.name()),
                jid, j.getName(), Operations.edit).save();

            break;
          }

          // c3/data/<journey-type>/ready?jid=<journey-id>
          case ready:
          {
            // 1) Puts the journey in the ready state. This is a state
            // between draft and published. This state can be used to review
            // a journey etc.

            String jid = req.getParameter("jid");

            //String jid = pathParts[1];
            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            // If journey does not exist, we will simply return
            if (j == null) return;
            j.ready();

            // 2) Once its ready, create a task for checkers to view this
            // artifact in their task list
            // check if a task is already created for that artifact. If it
            // exists, then do not create another one for the same artifact ID
            UserTask task = UserTask.getTaskForArtifact(ctx, jid);
            if (task != null) return;

            AccessControl.Operations op = (_journeyType == Type.c1)
                ? AccessControl.Operations.run
                : AccessControl.Operations.publish;
            AccessControl.ItemType type = AccessControl.ItemType
                .parse(_journeyType.name());

            // if no task is created, then create a new task and save it.
            task = UserTask.newInstance(ctx, type, jid, op);

            task.save();

            break;
          }

          // c3/data/journey/updateTimeInterval?journeyId=<journey-id>
          case updateTimeInterval:
          {
            String jid = req.getParameter("journeyId");
            
            //String jid = pathParts[1];
            String timePayload = ServletUtil.getPayload(req);

            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            if (j == null) return;
            j.setTimePayload(timePayload);
            j.save();

            // Audit
            ArtifactAudit.newInstance(ctx, ItemTypes.parse(_journeyType.name()),
                jid, j.getName(), Operations.edit).save();

            break;
          }
          // c3/data/journey/delete?jid=<journey-id>&type=<campaign|c1|journey>
          case delete:
          {
            String[] subParts = new String[2];
            String type = _journeyType.name();
            
            req.setAttribute("id", req.getParameter("jid"));
            req.setAttribute("type", type);
            subParts[0] = type;
            subParts[1] = cStr;
            new BXHandler().post().handle(ctx, subParts, req, resp);

            break;
          }

          // c3/data/journey/<resume|suspend>?jid=<journey-id>
          case resume:
          case suspend:
          {
            String jid = req.getParameter("jid");
            
            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            if (j == null) return;
            Map<String,Object> payload2 = j.getPayload2();
            String pl2Str = (payload2 != null && !payload2.isEmpty())
                ? new JsonMarshaller().serialize(payload2) : null;
                
            Map<String,Object> ret = TemplateUtils
                .getModuleConfigInstanceReferences(ctx, pl2Str);
            if (ret == null || !ret.containsKey("type")) return;
            TemplateConst.InstanceConfigType ict = TemplateConst.InstanceConfigType.valueOf((String) ret.get("type"));
            Set<String> refs = (Set<String>) ret.get("refs");

            ResponseMessage msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            if (ict.equals(TemplateConst.InstanceConfigType.primary))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "This " + _journeyType.name() + 
                    " is primary component of a module instance. Operation '"
                        + command.name()
                        + "' finished and was also applied on its supportive components: "
                        + references);
              }
                       
            }
            else if (ict.equals(TemplateConst.InstanceConfigType.supportive))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream().collect(Collectors.joining(", "));
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "This " + _journeyType.name() + 
                    " is supportive config instance and still being referenced in: "
                    + references);
                resp.getWriter().print(msg.toString());
                return;
              }
            
            }
            
            ModuleVisitor.VisitingOperation op;
            if (command.equals(PostCommand.suspend))
            {
              op = ModuleVisitor.VisitingOperation.suspend;
            }
            else
            {
              op = ModuleVisitor.VisitingOperation.resume;
            }
            
            Artifact art = new Artifact();
            art.setSubtype(_journeyType.name());
            art.setId(jid);
            TemplateArtifact ta = new TemplateArtifact(ctx, null, art,
                null, null, _journeyType.name(), null);

            ModuleVisitor mv = new ModuleVisitor(ctx, null, op);          
            mv.visit(ta);
            // Audit
            if (command.equals(PostCommand.suspend))
            {
              ArtifactAudit
                  .newInstance(ctx, ItemTypes.parse(_journeyType.name()), jid,
                      j.getName(), Operations.suspend)
                  .save();
            }
            else
            {
              ArtifactAudit
                  .newInstance(ctx, ItemTypes.parse(_journeyType.name()), jid,
                      j.getName(), Operations.resume).save();
            }

            msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            resp.getWriter().print(msg.toString());

            break;
          }

          // c3/data/journey/publish?jid=<journey-id>
          case publish:
          {
            String jid = req.getParameter("jid");
            String payload = ServletUtil.getPayload(req);

            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            if (j == null) return;
            
            // Allowed if TI already in ready or suspended state
            // Allowed if SI already in ready state
            if (j.getDefItem().getValues().get("state").equals("draft")) return;

            j.publish();

            // If there is any user task associated with this, we'll delete that
            // as well.
            UserTask task = UserTask.getTaskForArtifact(ctx, jid);
            if (task != null)
            {
              task.delete();
            }

            Map<String, Object> payloadMap = !payload.trim().isEmpty() ?
                new JsonMarshaller().readAsMap(payload) : new HashMap<String, Object>();

            if(payloadMap.containsKey("description"))
            {
              String description = (String) payloadMap.get("description");
              if(!description.trim().isEmpty())
              {
                ctx.put(z1.commons.Const.TE_PUBLISH_VERSION_DESCRIPTION, description);
                JourneyDesignUtils.updateLastExpChangeLog(ctx, j, _journeyType, false);
              }
            }

            // remove the jid from all the audience's payload2 if present ->
            // experienceList
            List<Segment> audiences = Segment.loadAll(ctx, false);
            for (Segment audience : audiences)
            {
              String audPayload2 = audience != null ? audience.getPayload2()
                  : null;
              if (audPayload2 != null && !audPayload2.isEmpty())
              {
                Map<String, Object> audPayload2Map = z1.commons.Const.jsonMarshaller
                    .readAsMap(audPayload2);
                if (audPayload2Map.containsKey("experienceList"))
                {
                  List<String> experiencesList = (List<String>) audPayload2Map
                      .get("experienceList");
                  experiencesList
                      .removeIf(experience -> experience.equals(j.getId()));
                  audience.setPayload2(z1.commons.Const.jsonMarshaller
                      .serialize(audPayload2Map));
                  audience.save();
                }
              }
            }

            // now add the jid in the associated audience's payload2 ->
            // experienceList
            Map<String, Object> jPayload2 = j.getPayload2();
            if (jPayload2 != null && jPayload2.containsKey("metaData"))
            {
              Map<String, Object> metaData = Optional
                  .ofNullable((Map<String, Object>) jPayload2.get("metaData"))
                  .orElse(new HashMap<>());
              if (metaData.containsKey("audience"))
              {
                String audienceId = String.valueOf(metaData.get("audience"));
                Segment aud = Segment.load(ctx, audienceId);
                String audPayload2 = aud != null ? aud.getPayload2() : null;
                if (audPayload2 != null && !audPayload2.isEmpty())
                {
                  Map<String, Object> audPayload2Map = z1.commons.Const.jsonMarshaller
                      .readAsMap(audPayload2);
                  if (!audPayload2Map.containsKey("experienceList"))
                  {
                    audPayload2Map.put("experienceList", new ArrayList<>());
                  }
                  List<String> experiencesList = (List<String>) audPayload2Map
                      .get("experienceList");
                  if (!experiencesList.contains(jid)) experiencesList.add(jid);
                  aud.setPayload2(z1.commons.Const.jsonMarshaller
                      .serialize(audPayload2Map));
                  aud.save();
                }
              }
            }

            // Audit
            ArtifactAudit.newInstance(ctx, ItemTypes.parse(_journeyType.name()),
                jid, j.getName(), Operations.publish).save();

            break;
          }
          
          // c3/data/campaign/execmode?id=<jID>&silent=<true|false>
          // OPT: payload
          case execmode:
          {
            String jid = req.getParameter("id");
            String isSilent = req.getParameter("silent");
            String payload = ServletUtil.getPayload(req);

            // return if jid is null OR either payload or 'silent' flag is
            // unavailable
            if (jid == null || jid.isEmpty() || ((payload == null || payload.isEmpty())
                && (isSilent == null || isSilent.isEmpty())))
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail);
              resp.getWriter().print(msg.toString());
              return;
            }

            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);

            // return if jid def not found.
            if (j == null || _journeyType != Type.campaign) return;

            // If payload is available simply update the whole payload.
            if (payload != null && !payload.isEmpty())
            {
              j = _updateJourney(ctx, j, payload, null, null);

              if (j == null) return;

              ActionUtils.invalidateScriptCache(ctx, jid);
            }
            else
            {
              boolean isSilentAction = Boolean.parseBoolean(isSilent);

              // Old def does not have 'silentmode' param.
              if (j.getParameterValue(
                  AbstractMobileAction.SILENT_ACTION) == null)
              {
                ParamDef pd = new ParamDef();
                pd.setName(AbstractMobileAction.SILENT_ACTION);
                pd.setValue(isSilent);
                j.getDef().getParams().add(pd);

                JourneyDesignUtils.updateExpChangeLog(ctx, j, _journeyType,
                    false);
              }
              else
              {
                List<ParamDef> params = j.getDef().getParams();

                for (ParamDef param : params)
                {
                  if (AbstractMobileAction.SILENT_ACTION.equals(param.getName())
                      && !isSilent.equals(param.getValue()))
                  {
                    param.setValue(Boolean.toString(isSilentAction));
                    
                    JourneyDesignUtils.updateExpChangeLog(ctx, j, _journeyType,
                        false);
                  }
                }
              }

              j.save();
            }

            // Audit
            ArtifactAudit
                .newInstance(ctx, ItemTypes.parse(_journeyType.name()), jid,
                    j.getName(), Operations.edit)
                .save();

            break;
          }

          // c3/data/journey/unpublish?jid=<journey-id>
          case unpublish:
          {
            String jid = req.getParameter("jid");
            
            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            if (j == null) return;
            j.ready();

            // Audit
            ArtifactAudit.newInstance(ctx, ItemTypes.parse(_journeyType.name()),
                jid, j.getName(), Operations.unpublish).save();

            break;
          }

          // c3/data/journey/edit?jid=<journey-id>
          case edit:
          {
            String jid = req.getParameter("jid");

            //String jid = pathParts[1];

            Journey j = Journey.forceLoadDef(ctx, jid, _journeyType);
            if (j == null) return;
            j.edit();

            // Audit
            ArtifactAudit.newInstance(ctx, ItemTypes.parse(_journeyType.name()),
                jid, j.getName(), Operations.edit).save();

            break;
          }

          // c3/data/<journey-type>/comment?jid=<journey-id> => Payload = Comment text
          case comment:
          {
            invalidateCache = false;
            String jid = req.getParameter("jid");
            
            //String jid = pathParts[1];
            String payload = ServletUtil.getPayload(req);
            UserComment uc = UserComment.newInstance(ctx,
                AccessControl.ItemType.parse(_journeyType.name()), jid,
                payload);

            User user = (User) ctx.getUser();
            if (user != null)
            {
              uc.getValues().put(UserComment.Fields.owner.name(),
                  user.getValues().get(User.Fields.fname.name()));
            }

            uc.save();
            break;
          }

          // c3/data/journey/runCampaignOnce?journeyId=<campaign-id>&type=<"campaign|journey|c1">
          case runCampaignOnce:
          {
            ULogger logger = ctx.getLogger(getClass());
            ResponseMessage msg = null;
            String journeyId = req.getParameter("journeyId");
            
            String jType = req.getParameter("type");
            if (jType == null) jType = "c1";
            
            // Check if segment recount all is in progress
            if (RunStatus.isRunning(ctx, QueryType.segment, ctx.getNamespace()))
            {
              if (logger.canLog())
              {
                logger.log(
                    "Counting all segments is in progress. Please wait until finished before running any Schedule Interaction.");
              }
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siFailed,
                  "Counting all segments is in progress. Please wait until finished before running any Schedule Interaction.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            // check if any segment targeted by this SI is in counting
            Journey j = Journey.forceLoadDef(ctx, journeyId, Type.c1);
            if (j == null
                || RunStatus.isRunning(ctx, QueryType.otc, j.getId()))
            {
              if (logger.canLog())
              {
                logger.log(
                    "Fail to run Schedule Interaction. Unable to find its definition or it's already running.");
              }
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siFailed,
                  "Fail to run Schedule Interaction. Unable to find its definition or it's already running.");
              resp.getWriter().print(msg.toString());
              return;
            }
            JourneyDef jDef = j.getDef();
            List<StateDef> states = jDef.getStates();
            for (StateDef state : states)
            {
              String sIdStr = state.getRef();
              if (sIdStr == null) continue;
              String[] sIdList = sIdStr.split(",");

              for (String sId : sIdList)
              {
                if (sId.equals(z1.commons.Const.ALL_USERS)) continue;

                Segment segment = Segment.load(ctx, sId, true);
                if (Utils.isUploadedSegment(segment)) continue;

                if (RunStatus.isRunning(ctx, QueryType.segment,
                    ctx.getNamespace() + "_" + sId))
                {
                  String sName = segment.getDef().getName();
                  if (logger.canLog())
                  {
                    logger.log("Segment '" + sName
                        + "' counting is in progress. Please wait until finished before running this Schedule Interaction.");
                  }
                  msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.siFailed, "Segment '" + sName
                          + "' counting is in progress. Please wait until finished before running this Schedule Interaction.");
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
            }
            msg = runCampaignOnce(ctx, journeyId, jType);            
            // A schedule channel custom config is created by runCampaignOnce. So channel need to clear cache
            App.notifyClearCache(ctx.getNamespace(), ChannelDefWrapper.PREFIX);

            if (msg != null)
            {
              resp.getWriter().print(msg.toString());
            }            
            break;
          }
          // c3/data/<journey-type>/park[?journeyId=<journey-id>]
          case stop:
          {
            String message = null;
            ResponseMessage msg = null;
            if (_journeyType != Type.c1)
            {
              message = "Request failed. Only Scheduled Experience can be stopped";
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siProcessing, message);
              resp.getWriter().print(msg.toString());
              return;
            }

            String jId = req.getParameter("journeyId");

            if (jId == null || jId.isEmpty())
            {
              message = "Request failed. Missing JourneyId.";
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siProcessing, message);
              resp.getWriter().print(msg.toString());
              return;
            }

            Journey j = Journey.forceLoadDef(ctx, jId, z1.c3.Journey.Type.c1);
            if (j == null) // not a valid jId for c1
            {
              message = "Unable to find the journey.";
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siProcessing, message);
              resp.getWriter().print(msg.toString());
              return;
            }
            // Call the ManagedJob to fetch the status
            Map<String, Object> status = RunStatusRequester.getInstance()
                .handleCommand(ctx, jId, RunCommand.stop);
            message = new JsonMarshaller().serialize(status);
            msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.siProcessing, message);
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/<journey-type>/park[?journeyId=<journey-id>]
          case park:
          {
            String message = null;
            ResponseMessage msg = null;
            if (_journeyType != Type.c1)
            {
              message = "Request failed. Only Scheduled Experience can be parked";
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siProcessing, message);
              resp.getWriter().print(msg.toString());
              return;
            }

            String jId = req.getParameter("journeyId");

            if (jId == null || jId.isEmpty())
            {
              message = "Request failed. Missing JourneyId.";
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siProcessing, message);
              resp.getWriter().print(msg.toString());
              return;
            }

            Journey j = Journey.forceLoadDef(ctx, jId, z1.c3.Journey.Type.c1);
            if (j == null) // not a valid jId for c1
            {
              message = "Unable to find the journey.";
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.siProcessing, message);
              resp.getWriter().print(msg.toString());
              return;
            }
            // Call the ManagedJob to fetch the status
            Map<String, Object> status = RunStatusRequester.getInstance()
                .handleCommand(ctx, jId, RunCommand.park);
            message = new JsonMarshaller().serialize(status);
            msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.siProcessing, message);
            resp.getWriter().print(msg.toString());
            break;
          }
        }

        if (invalidateCache)
        {
          App.notifyClearCache(ctx.getNamespace(), _journeyType.name());
        }

      }
    };

  }
  
  @SuppressWarnings("unchecked")
  public static ResponseMessage runCampaignOnce(UContext ctx, String journeyId, String jType)
  {
    ULogger logger = ctx.getLogger(JourneyHandler.class);
    ResponseMessage msg = null;
    Journey j = Journey.forceLoadDef(ctx, journeyId, z1.c3.Journey.Type.c1);

    if (j == null)
    {
      if (logger.canLog())
      {
        logger.log(String.format("Unable to find Schedule Interaction ID '%s'",
            journeyId));
      }

      msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.siFailed, String.format(
              "Unable to find Schedule Interaction ID '%s'", journeyId));
      return msg;
    }
    boolean isRunNow = false;
    List<StateDef> stateDefs = j.getDef().getStates();
    for (StateDef stateDef : stateDefs)
    {
      List<TimerDef> timerDefs = stateDef.getTimers();
      for (TimerDef timerDef : timerDefs)
      {
        if (TimerUnit.valueOf(timerDef.getUnit()).equals(TimerUnit.on)
            && timerDef.getValue().equals("now"))
        {
          isRunNow = true;
          break;
        }
      }
      if (isRunNow) break;
    }

    // ----------------------------------------------------------------------
    // Except for "RUN NOW" which is handled by the old code path,
    // all other types of C1 schedules are handles by
    // RecurringActionHandler
    // ----------------------------------------------------------------------
    if (isRunNow)
    {
      if (RunStatus.isRunning(ctx, QueryType.segment, ctx.getNamespace()))
      {
        if (logger.canLog())
        {
          logger.log(
              "Counting for all segments is in progress. We will not run any Schedule Interaction now.");
        }
        msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
            ResponseMessage.Type.siFailed,
            "Counting for all segments is in progress. We will not run any Schedule Interaction now.");
        return msg;
      }

      if (JourneyRuntimeUtils.targetSegmentIsCounting(ctx, j))
      {
        if (logger.canLog())
        {
          logger.log(
              "One or more segments targeted are in counting. We will not run any Schedule Interaction now.");
        }
        msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
            ResponseMessage.Type.siFailed,
            "One or more segments targeted are in counting. We will not run any Schedule Interaction now.");
        return msg;
      }

      logger.log(
          String.format("Journey %s is configured to run now.", j.getName()));

      PipelineExecutor pe = PipelineExecutor
          .createCollectorExecutor(Const.TIMER_EVENT_EXEC_NAME, ctx);
      Map<String, Object> params = new HashMap<>();
      params.put(InteractionEvaluator.JOURNEY_ID, journeyId);
      params.put(InteractionEvaluator.JOURNEY_TYPE, jType);
      params.put(InteractionEvaluator.JOURNEY_EXEC_CALLED_DURING_PUBLISH,
          "true");
      pe.setParams(params);
      pe.setAsyncExec(true);

      Job.Priority pri = Job.Priority.assignment;
      pe.setPriority(pri);

      pe.run();

      // Add the audit trail
      if ("c1".equalsIgnoreCase(jType))
      {
        ArtifactAudit.newInstance(ctx, ItemTypes.campaignonce, journeyId,
            j.getName(), Operations.run).save();
      }
      else
      {
        ArtifactAudit.newInstance(ctx, ItemTypes.campaign, journeyId,
            j.getName(), Operations.run).save();
      }

      msg = new ResponseMessage(ctx, ResponseMessage.Status.processing,
          ResponseMessage.Type.siProcessing,
          "Processing Schedule Interation " + j.getName() + " now...");
      return msg;
    }
    else // NOT A RUN NOW C1
    {
      // Construct a channel config of type "recurrence" to hold
      // recurring
      // timer information for this C1. See ZMOB-5005

      // Construct the payload for channel config
      // Example
      // payload:{"state":"published","type":"recurrence","c1id":"b5033c89-00ff-4901-a1ef-de8b54941ea1",
      // "dayRange":{"start":"2017-9-18","end":"2017-9-25"},"unit":"weekDays","value":"2|0830"}

      JsonMarshaller jm = new JsonMarshaller();

      Map<String, String> plMap = new HashMap<>();
      plMap.put("state", "published");
      plMap.put("type", "recurrence");
      plMap.put("c1id", journeyId);
      plMap.put("c1name", j.getName());

      String timePayload = j.getTimePayload();

      Map<String, Object> dayRangeMap = null;
      Map<String, Object> timePlMap = null;
      if (timePayload != null && timePayload.indexOf("dayRange") >= 0)
      {
        // There's dayRange configured
        // get startDate and endDate from timerPayload
        timePlMap = jm.readAsMap(timePayload);
        dayRangeMap = (Map<String, Object>) timePlMap.get("dayRange");
        plMap.put("startDate", (String) dayRangeMap.get("start"));
        plMap.put("endDate", (String) dayRangeMap.get("end"));
      }

      // If no dayRange configured, initialize these structures to store
      // "ref" to the
      // "recurrence" channel in C1 config's payload2, so that when C1
      // is deleted
      // the referenced "recurrence" channel also gets deleted
      if (dayRangeMap == null) dayRangeMap = new HashMap<>();

      if (timePlMap == null) timePlMap = new HashMap<>();

      // get timer unit and value from JourneyDef
      String unit = "";
      String value = "";

      if (!stateDefs.isEmpty() && !stateDefs.get(0).getTimers().isEmpty())
      {
        unit = stateDefs.get(0).getTimers().get(0).getUnit();
        value = stateDefs.get(0).getTimers().get(0).getValue();
      }

      plMap.put("unit", unit);
      plMap.put("value", value);

      String payload = new JsonMarshaller().serialize(plMap);

      ChannelDefWrapper cdr = ChannelDefWrapper.create(ctx);
      cdr.setName(j.getName());
      cdr.setDescription("Config to run OTC recurrently");
      cdr.setPayload(payload);
      cdr.setState(State.published);
      cdr.save();

      // Get the "recurrence" channel config ID and save it in payload2
      // of C1 config as reference "ref" for deletion later
      // "payload2" :
      // "{\"dayRange\":{\"start\":\"2017-9-18\",\"end\":\"2017-9-25\”,\”ref\”:\”95c261b5-9422-44c2-998e-8c0f0c142\”}}”

      String refId = cdr.getId();

      dayRangeMap.put("ref", refId);
      timePlMap.put("dayRange", dayRangeMap);
      String timePayloadWithRef = jm.serialize(timePlMap);

      j.setTimePayload(timePayloadWithRef);
      j.save();
      j.publish();

      if (logger.canLog())
      {
        logger.log(String.format(
            "Recurrence channel id:%s is created for Journey %s to handle its runs in future.",
            refId, j.getName()));
      }

      msg = new ResponseMessage(ctx, ResponseMessage.Status.processing,
          ResponseMessage.Type.siProcessing,
          "Interaction " + j.getName()
              + " is scheduled for later. System will run the interaction according to schedule.\n"
              + "Please note the following exception: if a Schedule Interaction is scheduled to be ran while the segment "
              + "recounting is in progress, the system will skip that schedule until segment recount is over.");
      return msg;
    }
  }
      
  // /////////////////////////////////////////////////
  // -------- PRIVATE IMPLEMENTATION -----------------

  // Loads a single journey info
  private Map<String, Object> _loadJourneyInfo(UContext ctx, Journey j,
      long fromCreationTime,Type jType)
  {
    return TemplateUtils.loadJourneyInfo(ctx, j, null, FilterOption.days, fromCreationTime,jType,false, true);
  } 
  
  // Update a journey object from a given payload
  @SuppressWarnings("unchecked")
  private Journey _updateJourney(UContext ctx, Journey j, String payload, String role, String isBU)
  {
    JsonMarshaller jm = new JsonMarshaller();
    Map<String, Object> jpMap = jm.readAsMap(payload);

    boolean isCreate = true;
    if (j == null)
    {
      j = Journey.create(ctx, (String) jpMap.get("name"), _journeyType);
    }
    else
    {
      isCreate = false;
    }

    // Load the detail
    j.setDescription((String) jpMap.get("description"));
    j.setName((String) jpMap.get("name"));
    Object obj = jpMap.get("payload");
    if (obj != null)
    {
      Map<String, Object> jPlMap = (Map<String, Object>) obj;

      if (!jPlMap.isEmpty())
      {
        // Check if control group settting has been changed.
        boolean cgChanged = false;
       
        // Get current control group setting if any before this update
        int currentTargetPercent = 100;
        if(j.getDef()!=null && j.getDef().getParams()!=null)
        {
          String z1target = (String) j.getParameterValue("z1_target");

          if (z1target != null)
          {
            currentTargetPercent = Integer.parseInt(z1target);
          }
        }
        
        // Get new control group setting if any in this update
        List<Map<String, Object>> params = (List<Map<String, Object>>) jPlMap.get("params");
        int newTargetPercent = 100;
        if (params != null && !params.isEmpty())
        {
          for (Map<String,Object> param : params)
          {
            String name = (String) param.get("name");
            if (name == null || !name.equals("z1_target")) continue;
            newTargetPercent = Integer.parseInt(param.get("value").toString());
            if (newTargetPercent < currentTargetPercent
                || newTargetPercent > currentTargetPercent)
              cgChanged = true;
          }
        }
        if (cgChanged)
        {
          // control group config has changed, reset totalPopulation and
          // cgPopulation for this journey in ControlGroupCube to zero to start fresh.
          ControlGroupRecorder.resetPopulations(ctx, j.getId());
        }
        
        j.setPayload(jm.serialize(jPlMap));
      }
    }
       
    // set createdBy attribute in payload2
    Map<String, Object> pl2Map = (Map<String, Object>) jpMap.get("payload2");
    if (isCreate)
    {
      if (pl2Map == null)
      {
        pl2Map = new HashMap<>(4);
      }
      pl2Map.put("createdBy", role);
      
      if (role != null && 
          (role.equals(User.Role.admin.name()) || role.equals(User.Role.sysadmin.name()) || 
              role.equals(User.Role.analyst.name())) && isBU != null)
      {
        pl2Map.put("isBU", isBU);
      }
    }
        
    if (pl2Map != null)
    {
      if (_journeyType == Type.campaign)
      {
        List<Map<String,Object>> triggeredBy = JourneyDesignUtils.getTriggeredByInfo(ctx, j);
        if (triggeredBy != null && !triggeredBy.isEmpty())
          pl2Map.put("triggeredBy", triggeredBy);
      }

      j.setTimePayload(jm.serialize(pl2Map));
    }

    String props = (String) jpMap.get(JourneySummaryInfo.properties.name());
    if (props != null)
    {
      DefinitionItem di = j.getDefItem();
      if (di == null) return null;
      di.getValues().put(DefinitionItem.Fields.properties.name(), props);
    }
    
    if (pl2Map != null)
    {
      if (pl2Map.get("artifactId") != null)
      {
        String artifactId = (String) pl2Map.get("artifactId");
        // don't do j.updatePayload2() OR overwrite "artifacts" in pl2 if an instance is created from BX module
        if (!artifactId.startsWith("udc.system.core")) j.updatePayload2();
      }
    }
    else
    {
      j.updatePayload2();
    }
    
    // collect action labels if any
    if (_journeyType.equals(Type.campaign))
    {
      JourneyDesignUtils.collectActionLabels(ctx, j);
    }
    
    j.edit();
    j.save();
    
    if (TemplateUtils.validateCompleteConfigured(j)) j.ready();

    _updateActionTemplateRef(ctx, j);
    if (isCreate || JourneyDesignUtils.isInfoParamsUpdate(ctx, j, _journeyType))
    {
      if (!z1.commons.Utils.isMarketPlace)
      {
        JourneyDesignUtils.updateExpChangeLog(ctx, j, _journeyType, false);
        // reset this journey's data from ManualAllocationRecorderCube on every update
        ManualActionRecorder.resetJourney(ctx, j.getId());
      }
    }
    
    // Update mutable params
    // Quick check if there are any mutable params defined.
    if (payload.contains("isMutable"))
    {
      // load lop
      String lopId = String.format("%s.%s",
          LOPHandler.SYSTEM_LOPS_PREFIX, j.getName());
      ListOfProperties lop = new ListOfProperties(ctx, lopId);
      Map<String, Object> mutableParams = JourneyDesignUtils
          .getMutableParamsFromJourneyUpdate(pl2Map);
      lop.payload(mutableParams);
      lop.save(false);
      ArtifactAudit.newInstance(ctx, ItemTypes.listOfProperties, lopId, lopId,
          Operations.edit).save();
    }

    return j;
  }
 
  /**
   * Updates action template references
   * 
   * @param uctx
   * @param j
   */
  private void _updateActionTemplateRef(UContext uctx, Journey j)
  {
    ParamDef paramDef = new ParamDef();
    j.getDef().getStates().forEach(stateDef -> {
      List<ActivitySelectorDef> asds = stateDef.getActivities();
      for (ActivitySelectorDef asd : asds)
      {
        // update action template references
        TemplateUtils.getActionTemplates(uctx, asd.getActions());
        asd.getActions().forEach(action -> {
          ParamDef templateRef = action.getParams().stream()
              .filter(param -> param.getName().equals("z1_template_ref"))
              .findFirst().orElse(paramDef);
          // clear scriptObject cache if actionTemplate is custom action
          if (templateRef.getValue() != null
              && (templateRef.getValue().startsWith(
                  "udc.system.core.ActionTemplateOOTB:customaction:ca_script:")
              || templateRef.getValue().startsWith(
                  "udc.system.core.NamespaceAction:customaction:")))
          {
            ScriptObject.invalidateCache(uctx,
                ScriptObject.ScriptType.customActionScript,
                (j.getId() + action.getName()).replaceAll(" ", ""));
          }
        });
      }
      List<TimerDef> timerDefs = stateDef.getTimers();
      for (TimerDef timeDef : timerDefs)
      {
        // update action template references
        TemplateUtils.getActionTemplates(uctx, timeDef.getActions());
        timeDef.getActions().forEach(action -> {
          ParamDef templateRef = action.getParams().stream()
              .filter(param -> param.getName().equals("z1_template_ref"))
              .findFirst().orElse(paramDef);
          // clear scriptObject cache if actionTemplate is custom action
          if (templateRef.getValue() != null
              && (templateRef.getValue().startsWith(
                  "udc.system.core.ActionTemplateOOTB:customaction:ca_script:")
              || templateRef.getValue().startsWith(
                  "udc.system.core.NamespaceAction:customaction:")))
          {
            ScriptObject.invalidateCache(uctx,
                ScriptObject.ScriptType.customActionScript,
                (j.getId() + action.getName()).replaceAll(" ", ""));
          }
        });
      }
    });
  }
  
  /**
   * Returns true if user is admin, false otherwise.
   * For Admin ==> isBUUser == true && isDeveloper == true
   * 
   * @param isBUUser
   * @param isDeveloper
   * @return
   */
  private boolean _isAdmin(boolean isBUUser, boolean isDeveloper)
  {
    return isBUUser && isDeveloper;
  }

 // Gets the stat for a given campaign or journey
  private Map<String, Object> _getJourneySummaryStat(UContext ctx, Journey j,
      Type journeyType, long fromCreationTime, Map<String, Object> channelMap,
      FilterOption p, String isBU)
  {
    ExeMode exeMode = ExeMode.live;
   
    DefinitionItem item = j.getDefItem();
    if (item == null) return null;
    
    Map<String, Object> pl2map = j.getPayload2();
    if (pl2map != null && (!pl2map.isEmpty()))
    {
      // filter instances for BU view
      String st = (String) item.getValues()
          .get(DefinitionItem.Fields.state.name());
      String isCratedByBU = "";
      if (pl2map.get("isBU") != null) isCratedByBU = pl2map.get("isBU").toString();
      if ((isBU != null && isBU.equalsIgnoreCase("true")) &&
          (!"true".equalsIgnoreCase(isCratedByBU)) &&
          (st.equalsIgnoreCase("draft") || st.equalsIgnoreCase("ready"))) return null;
    }
    
    Long creationTime = (Long) item.getValues()
        .get(DefinitionItem.Fields.timestamp.name());
    Long lastUpdatedTime = (Long) item.getValues()
        .get(DefinitionItem.Fields.lastUpdated.name());
    switch (p)
    {
      case active:
        if (!Journey.isActiveCampaign(ctx, j, channelMap, journeyType))        
        {
          return null;
        }
        break;
      case inactive:
        if (!Journey.isInActiveCampaign(ctx, j, channelMap, journeyType))
        {
          return null;
        }
        break;
      case draft:
        String state = (String)item.getValues().get("state");
        if (!("draft".equals(state) || "ready".equals(state))) return null;
        break;
      case days:
        if (fromCreationTime > 0)
        {
          // If update time is earlier than the creating time (if this happens
          // for
          // some reason)
          // use the update time instead.
          long cTime = (creationTime != null) ? creationTime : 0L;
          long uTime = (lastUpdatedTime != null) ? lastUpdatedTime : 0L;
          cTime = ((cTime == 0) || (cTime > uTime)) ? uTime : cTime;

          if (cTime < fromCreationTime) return null;
        }
    }
    Map<String, Object> statInfo = new java.util.HashMap<>(10);
    statInfo.put(JourneySummaryInfo.id.name(), j.getId());
    String journeyId = j.getId();
    JourneySegmentStatsRecorder jsr = new JourneySegmentStatsRecorder(ctx);

    // Get the analytic data
    long users = 0L;
    long actionSentSuccess = 0L;
    long actionSentFailure = 0L;
    // Delivered := Action has reached the user device. Valid only for Google
    // push notification. For other cases, we'll send the number as
    // actionSuccess
    Long actionDelivered = 0L;
    long actionViewed = 0L;
    long conversion = 0L;
    long cgConversion = 0L; // Control group conversion
    long userReached = 0L; // unique user count that we have reached

    String changeId = null;
    boolean isMigrated = false;
    boolean isPreBx = false;

    ExpChangeLog changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
    if (changeLog == null)
    {
      // it check if change log not exist
      // then add it using latest change for journeyId
      JourneyDesignUtils.updateExpChangeLog(ctx, j, _journeyType, isMigrated);
      changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
      if (changeLog == null) return null; // draft state
      changeId= changeLog.getId();     
    }

    changeId= changeLog.getId();     
    if (changeId.equals("1"))
    {
      isMigrated = changeLog.isMigrated();
      if (isMigrated)
      {
        isPreBx = true;
      }
    }
    else
    {
      ExpChangeLog firstChangeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx,
          journeyId, 1);
      if (firstChangeLog != null && firstChangeLog.isMigrated())
      {
        isPreBx = true;
      }
    }

    if (journeyType.equals(Type.campaign))
    {
      Object mode = j.getParameterValue(AbstractMobileAction.SILENT_ACTION);
      boolean isSilent = mode == null ? false: Boolean.parseBoolean(mode.toString());
      
      if(isSilent)
      {
        exeMode = ExeMode.silent;
      }
      Journey.Step step = new Journey.Step(journeyId, 0);
      String stepId = step.getRef();
      actionSentSuccess = ExperienceTotalStatsRecorder.getActionsTotalSent(ctx,
          journeyId, exeMode);
      actionSentFailure = ExperienceTotalStatsRecorder
          .getActionsTotalNotSent(ctx, journeyId, exeMode);
      actionViewed = ExperienceTotalStatsRecorder.getActionsTotalReached(ctx,
          journeyId, exeMode);
      actionDelivered = ActionAckStatRecorder.getTotalActionCount(ctx, stepId,
          journeyType, ActionDeliveryStatus.delivered, exeMode);
      
      // if journey has prebx stats add into bx to merge total stats
      if (isPreBx)
      {
        actionSentSuccess += ActionAckStatRecorder.getActionCount(ctx, stepId,
            journeyType, ActionDeliveryStatus.success);
        actionSentFailure += ActionAckStatRecorder.getActionCount(ctx, stepId,
            journeyType, ActionDeliveryStatus.failure);
        actionViewed += ActionAckStatRecorder.getActionCount(ctx, stepId,
            journeyType, ActionDeliveryStatus.reached);
        actionDelivered += ActionAckStatRecorder.getActionCount(ctx, stepId,
            journeyType, ActionDeliveryStatus.delivered);
      }
      if (actionDelivered == null) actionDelivered = actionSentSuccess;
 

      // For campaigns calculate the conversion so that it can be reported.
      // There must be at the
      // most 2 journey steps for campaigns. If there is no "target" step
      // defined, the conversion
      // will be reported as 0.
      JourneyDef def = j.getDef();
      if (def == null) return null;
      List<StateDef> stateDefs = def.getStates();
      if (stateDefs.size() > 1)
      {
        Journey.Step stepFrom = new Journey.Step(j.getId(), 0);
        Journey.Step stepTo = new Journey.Step(j.getId(), 1);

        // conversion = jsr.getConversionNumbers(journeyId, stepFrom.getRef(),
        // stepTo.getRef());
        cgConversion = jsr.getConversionNumbersWithNoAction(journeyId,
            stepFrom.getRef(), stepTo.getRef(), changeId, isMigrated, isPreBx, exeMode);

      }
      long added = 0L;
      long reached = 0L;
      List<Map<String, Object>> populationInfos = jsr.getJourneyStatePopulation(
          journeyId, stepId, "*", isMigrated, isPreBx, exeMode);

      for (Map<String, Object> populationInfo : populationInfos)
      {
        Number add = (Number) populationInfo.get("added");
        added = added + ((add == null) ? 0L : add.longValue());

        Number reach = (Number) populationInfo.get("reached");
        reached = reached + ((reach == null) ? 0L : reach.longValue());

        Number lc = (Number) populationInfo.get("deleted");
        conversion = conversion + ((lc == null) ? 0L : lc.longValue());
      }

      users = added;
      userReached = reached;
    }
    else if (journeyType.equals(Type.c1))
    {
      Journey.Step step = new Journey.Step(journeyId, 0);
      String stepId = step.getRef();

      JourneyDef def = j.getDef();
      if (def == null)
      {
        return null;
      }
      List<StateDef> stateDefs = def.getStates();
      if (stateDefs.size() > 1)
      {
        Journey.Step stepFrom = new Journey.Step(j.getId(), 0);
        Journey.Step stepTo = new Journey.Step(j.getId(), 1);
        cgConversion = jsr.getConversionNumbersWithNoAction(journeyId,
            stepFrom.getRef(), stepTo.getRef(), changeId, isMigrated, isPreBx, exeMode);
      }

      long added = 0L;
      List<Map<String, Object>> populationInfos = jsr.getJourneyStatePopulation(
          journeyId, stepId, "*", isMigrated, isPreBx, exeMode);
      for (Map<String, Object> populationInfo : populationInfos)
      {
        Number add = (Number) populationInfo.get("added");
        added = added + ((add == null) ? 0L : add.longValue());

        Number lc = (Number) populationInfo.get("deleted");
        conversion = conversion + ((lc == null) ? 0L : lc.longValue());
      }

      // SI is not showing this metric.
      // statInfo.put(JourneySummaryInfo.userTargeted.name(), targetUsers);
      actionDelivered = ActionAckStatRecorder.getTotalActionCount(ctx, stepId,
          journeyType, ActionDeliveryStatus.delivered, exeMode);

      // Total number of times actions were sent for this journey
      long actionsSent = ExperienceTotalStatsRecorder.getActionsTotalSent(ctx,
          journeyId, exeMode);
      // Total number of times actions reached the users
      long actionsReached = ExperienceTotalStatsRecorder
          .getActionsTotalReached(ctx, journeyId, exeMode);
      
      // if journey has isPreBxprebx stats add into bx to merge total stats
      if (isPreBx)
      {
        actionDelivered += ActionAckStatRecorder.getActionCount(ctx, stepId,
            journeyType, ActionDeliveryStatus.delivered);
        actionsSent += ActionsTargetedMetrics.getActionsSent(ctx, journeyId);

        // Total number of times actions reached the users
        actionsReached += ActionStateMetrics.getActionsReachedCount(ctx,
            journeyId);
      }
      if (actionDelivered == null) actionDelivered = actionSentSuccess;

      statInfo.put(JourneySummaryInfo.users.name(), added);
      statInfo.put(JourneySummaryInfo.aSent.name(), actionsSent);
      statInfo.put(JourneySummaryInfo.aDelivered.name(), actionDelivered); // hidden
                                                                           // field
                                                                           // in
      UsersTargetedStatsRecorder ur = new UsersTargetedStatsRecorder(ctx);
      long targetUsers = ur.getUsersTargetedPopulation(journeyId);
      statInfo.put(JourneySummaryInfo.userTargeted.name(), targetUsers);
      // UI
      statInfo.put(JourneySummaryInfo.reached.name(), actionsReached);
      statInfo.put(JourneySummaryInfo.controlGroupConverted.name(),
          cgConversion);

      // UI uses this for sent instead of using sent!!!!
      statInfo.put(JourneySummaryInfo.aReceived.name(), actionsSent);
      // UI calls this viewed, lets change this to reached !!
      statInfo.put(JourneySummaryInfo.aViewed.name(), actionsReached);

      // Total number of times users interacted with any action as part of this
      // journey

      long actionsInteractedWith = ExperienceTotalStatsRecorder
          .getActionsTotalInteracted(ctx, journeyId,isPreBx, exeMode);
      statInfo.put(JourneySummaryInfo.aInteracted.name(),
          actionsInteractedWith);

      statInfo.put(JourneySummaryInfo.converted.name(), conversion);
      Collection<Object> reasons = null;
      reasons = new ActionUtils(ctx, journeyId).actionsNotSentReasons(changeId);

      statInfo.put(JourneySummaryInfo.actionNotSentReasons.name(), reasons);

      return statInfo;
    }
    else
    {
      // Compute the user counts
      // We will calculate the total population we had so far
      JourneyDef def = j.getDef();
      List<StateDef> stateDefs = def.getStates();
      int cnt = stateDefs.size();
      int index = 0;
      for (StateDef sd : stateDefs)
      {
        Journey.Step step = new Journey.Step(journeyId, index);
        index++;
        String stepId = step.getRef();

        String nextStepId = null;
        if (index < cnt)
        {
          Journey.Step nextStep = new Journey.Step(journeyId, index);
          nextStepId = nextStep.getRef();
        }
        Long aSuccess = ExperienceTotalStatsRecorder.getActionsTotalSent(ctx,
            journeyId, exeMode);
        Long aDelivered = ActionAckStatRecorder.getTotalActionCount(ctx, stepId,
            _journeyType, ActionDeliveryStatus.delivered, exeMode);
        actionSentSuccess += aSuccess;
        actionSentFailure += ExperienceTotalStatsRecorder
            .getActionsTotalNotSent(ctx, journeyId, exeMode);
        actionViewed += ExperienceTotalStatsRecorder.getActionsTotalReached(ctx,
            journeyId, exeMode);
        // if journey has prebx stats add into bx to merge total stats
        if (isPreBx)
        {
          aSuccess = ActionAckStatRecorder.getActionCount(ctx, stepId,
              _journeyType, ActionDeliveryStatus.success);
          actionSentSuccess += aSuccess;
          actionSentFailure += ActionAckStatRecorder.getActionCount(ctx, stepId,
              _journeyType, ActionDeliveryStatus.failure);
          actionViewed += ActionAckStatRecorder.getActionCount(ctx, stepId,
              _journeyType, ActionDeliveryStatus.reached);
          aDelivered += ActionAckStatRecorder.getActionCount(ctx, stepId,
              _journeyType, ActionDeliveryStatus.delivered);
        }

        if (aDelivered == 0)
        {
          aDelivered = aSuccess;
        }
        actionDelivered += aDelivered;

        long added = 0L;
        long reached = 0L;
        long deleted = 0L;
        List<Map<String, Object>> populationInfos = jsr
            .getJourneyStatePopulation(journeyId, stepId, "*", isMigrated,
                isPreBx, exeMode);
        for (Map<String, Object> populationInfo : populationInfos)
        {
          Number add = (Number) populationInfo.get("added");
          added = added + ((add == null) ? 0L : add.longValue());

          Number reach = (Number) populationInfo.get("reached");
          reached = reached + ((reach == null) ? 0L : reach.longValue());

          Number lc = (Number) populationInfo.get("deleted");
          deleted = deleted + ((lc == null) ? 0L : lc.longValue());
        }

        long userCount = added - deleted;
        if (userCount < 0) userCount = 0L;

        users += userCount;
        userReached += reached;
        // People who are deleted from a step are converted
        conversion += deleted;

        if (nextStepId != null)
        {
          cgConversion += jsr.getConversionNumbersWithNoAction(journeyId,
              stepId, nextStepId, "*", isMigrated, isPreBx, exeMode);
        }
      }
    }

    Long totalSent = actionSentSuccess + actionSentFailure;
    statInfo.put(JourneySummaryInfo.users.name(), users);

    UsersTargetedStatsRecorder ur = new UsersTargetedStatsRecorder(ctx);
    ur.getUsersTargetedPopulation(journeyId);
    long targetUsers = ur.getUsersTargetedPopulation(journeyId);

    statInfo.put(JourneySummaryInfo.userTargeted.name(), targetUsers);
    statInfo.put(JourneySummaryInfo.reached.name(), userReached);
    statInfo.put(JourneySummaryInfo.aSent.name(), totalSent);
    statInfo.put(JourneySummaryInfo.aDelivered.name(), actionDelivered);
    statInfo.put(JourneySummaryInfo.aReceived.name(), actionSentSuccess);
    statInfo.put(JourneySummaryInfo.aViewed.name(), actionViewed);
    statInfo.put(JourneySummaryInfo.converted.name(), conversion);
    statInfo.put(JourneySummaryInfo.controlGroupConverted.name(), cgConversion);
    return statInfo;
  }

  // ...................................................
  // Returns the stat for interactions with messages sent from a journey /
  // campaign step
  private List<Map<String, Object>> _populateIntStat(UContext ctx,
      String journeyId, String journeyStepRef, String changeLogId,
      boolean isMigrated)
  {
    // To populate the interactions stat for each content sent,
    // we need to query ActionAckStatCube twice
    List<Map<String, Object>> retMergedList = new java.util.ArrayList<>(20);

    // Get the stat for interacted content(s)

    List<Map<String, Object>> resInteracted = ActionAckStatRecorder
        .getInteractionStat(ctx, journeyStepRef, _journeyType,
            ActionDeliveryStatus.interacted, changeLogId, isMigrated);

    // Get the stat for viewed content(s)

    List<Map<String, Object>> resViewed = null;
    resViewed = ActionAckStatRecorder.getContentStat(ctx, journeyStepRef,
        _journeyType, ActionDeliveryStatus.success, changeLogId, isMigrated);

    // We'll merge the two list based on the content ID. If we don't find a
    // match in the 2 lists, we'll put the corresponding number as 0.
    // The 2 fields that we are interested in are "interacted" and 'count'.

    List<Map<String, Object>> unmatchedViewedList = new java.util.ArrayList<>(
        20);

    for (Map<String, Object> recViewed : resViewed)
    {
      // We'll get the content ID from resViewed, find it in resInteracted and
      // merge the search result to add to the merged list.
      String contentId = null;
      if (isMigrated)
      {
        contentId = (String) recViewed.get(ReqFields.contentId.name());
      }
      if (contentId == null)
      {
        contentId = (String) recViewed.get("actionName");
      }
      if (contentId == null) continue;

      Map<String, Object> matchedRec = null;

      int index = 0;
      for (Map<String, Object> resInt : resInteracted)
      {
        String cId = null;
        if (isMigrated)
        {
          cId = (String) resInt.get(ReqFields.contentId.name());
        }
        if (cId == null)
        {
          cId = (String) resInt.get("actionName");
        }
        if ((cId != null) && cId.equalsIgnoreCase(contentId))
        {
          // When the contentId in viewed matches the id in interacted
          // remove the item from Interacted and later add it to the merged list
          matchedRec = resInteracted.remove(index);
          break;
        } 
        index++;
      }

      if (matchedRec != null)
      {
        // Adding the matched item from above loop, to the merged list

        Map<String, Object> item = _getMsgStatItem(ctx, recViewed, matchedRec,
            true, isMigrated);
        if (item != null) retMergedList.add(item);
      }
      else
      {
        // Track the viewed records that did not find a match in the interacted
        // records
        unmatchedViewedList.add(recViewed);
      }
    }

    // After all matched items have been merged
    // Add all unmatched items to the merge list
    // with the corresponding value of "interacted" set to 0
    for (Map<String, Object> item : unmatchedViewedList)
    {
      Map<String, Object> newItem = _getMsgStatItem(ctx, item, null, true,
          isMigrated);
      if (newItem != null) retMergedList.add(newItem);
    }

    return retMergedList;
  }

  
//...................................................
 // Returns the stat for interactions with messages sent from a journey /
 // campaign step
  @Deprecated
 private List<Map<String, Object>> _populateIntStat(UContext ctx,
     String journeyId, String journeyStepRef, String changeLogId,
     String fromDate, String toDate,boolean isMigrated)
 {
   // To populate the interactions stat for each content sent,
   // we need to query ActionAckStatCube twice
   List<Map<String, Object>> retMergedList = new java.util.ArrayList<>(20);

   // Get the stat for interacted content(s)

   List<Map<String, Object>> resInteracted = ActionAckStatRecorder
       .getInteractionStat(ctx, journeyStepRef, _journeyType,
           ActionDeliveryStatus.interacted, changeLogId,fromDate,toDate);

   // Get the stat for viewed content(s)

   List<Map<String, Object>> resViewed = null;
   resViewed = ActionAckStatRecorder.getContentStat(ctx, journeyStepRef,
       _journeyType, ActionDeliveryStatus.success, changeLogId, isMigrated);

   // We'll merge the two list based on the content ID. If we don't find a
   // match in the 2 lists, we'll put the corresponding number as 0.
   // The 2 fields that we are interested in are "interacted" and 'count'.

   List<Map<String, Object>> unmatchedViewedList = new java.util.ArrayList<>(
       20);

   for (Map<String, Object> recViewed : resViewed)
   {
     // We'll get the content ID from resViewed, find it in resInteracted and
     // merge the search result to add to the merged list.
     String contentId = null;
     if (isMigrated)
     {
       contentId = (String) recViewed.get(ReqFields.contentId.name());
     }
     if (contentId == null)
     {
       contentId = (String) recViewed.get("actionName");
     }
     if (contentId == null) continue;

     Map<String, Object> matchedRec = null;

     int index = 0;
     for (Map<String, Object> resInt : resInteracted)
     {
       String cId = null;
       if (isMigrated)
       {
         cId = (String) resInt.get(ReqFields.contentId.name());
       }
       if (cId == null)
       {
         cId = (String) resInt.get("actionName");
       }
       if ((cId != null) && cId.equalsIgnoreCase(contentId))
       {
         // When the contentId in viewed matches the id in interacted
         // remove the item from Interacted and later add it to the merged list
         matchedRec = resInteracted.remove(index);
         break;
       } 
       index++;
     }

     if (matchedRec != null)
     {
       // Adding the matched item from above loop, to the merged list

       Map<String, Object> item = _getMsgStatItem(ctx, recViewed, matchedRec,
           true, isMigrated);
       if (item != null) retMergedList.add(item);
     }
     else
     {
       // Track the viewed records that did not find a match in the interacted
       // records
       unmatchedViewedList.add(recViewed);
     }
   }

   // After all matched items have been merged
   // Add all unmatched items to the merge list
   // with the corresponding value of "interacted" set to 0
   for (Map<String, Object> item : unmatchedViewedList)
   {
     Map<String, Object> newItem = _getMsgStatItem(ctx, item, null, true,
         isMigrated);
     if (newItem != null) retMergedList.add(newItem);
   }

   return retMergedList;
 }

    // ...................................................
    // Returns the stat for interactions with messages sent from a journey /
    // campaign step
    private List<Map<String, Object>> _populateDailyIntStat(UContext ctx,
        String journeyId, String journeyStepRef, String changeLogId,
        boolean isMigrated, String f, String t)
  {
    // Get the stat for interacted
    List<Map<String, Object>> resInteracted = ActionAckStatRecorder
        .getActionStat(ctx, journeyStepRef, _journeyType,
            ActionDeliveryStatus.interacted, changeLogId, isMigrated, f, t);

    // Get the stat for dismissed
    List<Map<String, Object>> resDismissed = ActionAckStatRecorder
        .getActionStat(ctx, journeyStepRef, _journeyType,
            ActionDeliveryStatus.dismissed, changeLogId, isMigrated, f, t);

    // Get the stat for interactions (messages) sent
    List<Map<String, Object>> resSuccess = ActionAckStatRecorder.getActionStat(
        ctx, journeyStepRef, _journeyType, ActionDeliveryStatus.success,
        changeLogId, isMigrated, f, t);

    List<Map<String, Object>> unmatchedViewedList = new java.util.ArrayList<>(
        20);
    List<Map<String, Object>> retMergedList = new java.util.ArrayList<>(20);

    for (Map<String, Object> recSuc : resSuccess)
    {
      // We'll get the content ID from resViewed, find it in resInteracted and
      // merge the search result to add to the merged list.
      String contentId = null;
      if (isMigrated)
      {
        contentId = (String) recSuc.get(ReqFields.contentId.name());
      }
      if (contentId == null)
      {
        contentId = (String) recSuc.get("actionName");
      }
      if (contentId == null) continue;
      String exeMode = (String) recSuc.get("exeMode");
      Map<String, Object> matchedInteractedRec = null;
      int index = 0;
      for (Map<String, Object> resInt : resInteracted)
      {
        String cId = null;
        if (isMigrated)
        {
          cId = (String) resInt.get(ReqFields.contentId.name());
        }
        if (cId == null)
        {
          cId = (String) resInt.get("actionName");
        }
        String eMode = (String) resInt.get("exeMode");

        if ((cId != null) && cId.equalsIgnoreCase(contentId)
            && (eMode == exeMode
                || (exeMode != null && eMode != null && exeMode.equals(eMode))))
        {
          // When the contentId in viewed matches the id in interacted
          // remove the item from Interacted and later add it to the merged list
          matchedInteractedRec = resInteracted.remove(index);
          break;
        }
        index++;
      }

      Map<String, Object> matchedDismissedRec = null;
      int idx = 0;
      for (Map<String, Object> resDism : resDismissed)
      {
        String cId = null;
        if (isMigrated)
        {
          cId = (String) resDism.get(ReqFields.contentId.name());
        }
        if (cId == null)
        {
          cId = (String) resDism.get("actionName");
        }
        String eMode = (String) resDism.get("exeMode");

        if ((cId != null) && cId.equalsIgnoreCase(contentId)
            && (eMode == exeMode
                || (exeMode != null && eMode != null && exeMode.equals(eMode))))
          if ((cId != null) && cId.equalsIgnoreCase(contentId))
        {
          // When the contentId in viewed matches the id in dismissed
          // remove the item from Dismissed and later add it to the merged list
          matchedDismissedRec = resDismissed.remove(idx);
          break;
        }
        idx++;
      }

      if (matchedInteractedRec != null || matchedDismissedRec != null)
      {
        // Adding the matched item from above loop, to the merged list

        Map<String, Object> item = _getDailyStatItems(ctx, recSuc,
            matchedInteractedRec, matchedInteractedRec);
        if (item != null) retMergedList.add(item);
      }
      else
      {
        // Track the viewed records that did not find a match in the interacted
        // records
        unmatchedViewedList.add(recSuc);
      }
    }

    // After all matched items have been merged
    // Add all unmatched items to the merge list
    // with the corresponding value of "interacted" set to 0
    for (Map<String, Object> item : unmatchedViewedList)
    {
      Map<String, Object> newItem = _getDailyStatItems(ctx, item, null, null);
      if (newItem != null) retMergedList.add(newItem);
    }

    return retMergedList;
  }

  // ...................................................
  // Returns the stat for messages used from a journey / campaign step
  // private List<Map<String, Object>> _populateMsgStat(UContext ctx,
  // String journeyId, String journeyStepRef,String changeDate,String
  // changeInfo)
  // {
  // // To populate the viewed and conversion stat for each content sent,
  // // we need to query multiple cubes.
  // List<Map<String, Object>> retMergedList = new java.util.ArrayList<>(20);
  //
  // // Get the stat for viewed contents
  // List<Map<String, Object>> resViewed = ActionAckStatRecorder.getContentStat(
  // ctx, journeyStepRef, _journeyType, ActionDeliveryStatus.reached,
  // changeDate, changeInfo);
  //
  // // Get the stat for the contents that helped a conversion
  // List<Map<String, Object>> resConverted = new JourneySegmentStatsRecorder(
  // ctx).getContentConversionStat(journeyId, changeDate, changeInfo);
  //
  // // We'll merge the two list based on the content ID. If we don't find a
  // // match in the 2 lists, we'll
  // // put the corresponding number as 0. The 2 fields that we are interested
  // // are "conversion" and 'count'.
  //
  // List<Map<String, Object>> unmatchedViewedList = new java.util.ArrayList<>(
  // 20);
  //
  // for (Map<String, Object> recViewed : resViewed)
  // {
  // // We'll get the content ID and find out that from the other list and
  // // merge
  // // both to add to the resulting list.
  // String contentId = (String) recViewed.get("actionName");
  // if (contentId == null) continue;
  //
  // Map<String, Object> matchedRec = null;
  //
  // int index = 0;
  // for (Map<String, Object> recConv : resConverted)
  // {
  // String cId = (String) recConv.get("actionName");
  // if ((cId != null) && cId.equalsIgnoreCase(contentId))
  // {
  // // We matched. We'll delete this item from the list and remember for
  // // merge and add to resulting list.
  // matchedRec = resConverted.remove(index);
  // break;
  // }
  //
  // index++;
  // }
  //
  // if (matchedRec != null)
  // {
  // // We have a match with the viewed record.
  // // Merge the two selectively
  //
  // Map<String, Object> item = _getMsgStatItem(ctx, recViewed, matchedRec,
  // false);
  // if (item != null) retMergedList.add(item);
  // }
  // else
  // {
  // // Remember that this record didn't match
  // unmatchedViewedList.add(recViewed);
  // }
  // }
  //
  // // By now we have all items collected that matched. Now we need to look at
  // // the list of
  // // items that didn't match, if any.
  //
  // // Anything left from viewed list?
  // for (Map<String, Object> item : unmatchedViewedList)
  // {
  // Map<String, Object> newItem = _getMsgStatItem(ctx, item, null, false);
  // if (newItem != null) retMergedList.add(newItem);
  // }
  //
  // // Anything left from conversion list? If we don't find that in viewed,
  // // we'll
  // // ignore them. Conversion for a content without viewing it makes no sense.
  // // for (Map<String, Object> recConv : resConverted)
  // // {
  // // if (recConv.isEmpty()) continue;
  // // Map<String, Object> newItem = _getMsgStatItem(ctx, null, recConv,
  // false);
  // // // Here we'll just add "count" (view count for the contentId) to 0.
  // // if (newItem != null) retMergedList.add(newItem);
  // // }
  //
  // return retMergedList;
  // }


  // .....................................................
  // Gets the message stat for a given content ID
  private Map<String, Object> _getMsgStatItem(UContext ctx,
      Map<String, Object> viewStat, Map<String, Object> convStat,
      Boolean interactedOnly, boolean isMigrated)
  {
    final String CONTENT = "content";
    final String VIEWED = "viewed";
    final String CONVERTED = "converted";
    final String INTERACTED = "interacted";

    Number viewed = 0;
    Number converted = 0;
    Number interacted = 0;

    String actionName = null;
    String contentId = null;
    if (viewStat != null)
    {
      if (isMigrated)
      {
        contentId = (String) viewStat.get(ReqFields.contentId.name());
      }
      if (contentId == null)
      {
        actionName = (String) viewStat.get("actionName");
      }
    }
    else if (convStat != null)
    {
      if (isMigrated)
      {
        contentId = (String) convStat.get(ReqFields.contentId.name());
      }
      if (contentId == null)
      {
        actionName = (String) convStat.get("actionName");
      }
    }

    if (actionName == null && contentId == null) return null;

    // If content ID == empty, it indicates that we haven't taken any action.
    // We will return null to indicate that we'll ignore the "no action" from
    // the A/B test data
    if ((actionName == null || actionName.isEmpty())
        && (contentId == null || contentId.isEmpty()))
      return null;

    // Get content name from content ID as stored in store
    if (actionName == null && isMigrated)
    {
      try
      {
        KBResponse kbr = KBDocumentRetreiver.getContent(ctx, contentId);
        // ContentTuple ct = kbr.getContentTuple(KBResponse.DEFAULT_LANGUAGE);
        // kbr.setTitle(ct.getTitle());
        actionName = kbr.getTitle();
      }
      catch (Exception e)
      {
        actionName = null;
      }

      if (actionName == null) return null;
    }
    // Only add the selected data. If a map is null put 0 instead.
    if ((viewStat != null) && !viewStat.isEmpty())
    {
      Object obj = viewStat.get("count");
      if (obj != null)
      {
        viewed = (Number) obj;
      }
    } 
    if ((convStat != null) && !convStat.isEmpty())
    {
      if (!interactedOnly)
      {
        Object obj = convStat.get("conversion");
        if (obj != null)
        {
          converted = (Number) obj;
        }
      }
      else
      {
        Object obj = convStat.get("interacted");
        if (obj != null)
        {
          interacted = (Number) obj;
        }
      }
    }

    // If viewed == 0, we will fore set conversion == 0.
    // It makes no sense to show a conversion for a content when no
    // one viewed the item.
    // TODO Why are we getting that strange data ??
    if (viewed.longValue() == 0)
    {
      converted = 0;
      interacted = 0;
    }

    Map<String, Object> item = new java.util.HashMap<>(3);
    item.put(CONTENT, actionName);
    item.put(VIEWED, viewed);
    if (!interactedOnly)
    {
      item.put(CONVERTED, converted);
    }
    else
    {
      item.put(INTERACTED, interacted);
    }

    return item;
  }

  // ...................................................
  // Returns the stat for action used from a journey / campaign step
  private Map<String, Object> _populateActionStatItem(UContext ctx,
      Journey journey, int stepIndex, ExpChangeLog changeLog,
      boolean isMigrated)
  {
    if (changeLog == null) return new java.util.HashMap<String, Object>();
    
    final String TOTAL_CNT = "totalCount";
    final String TARGET_CNT = "targetCount";
    final String TOTAL_CONV = "totalConversion";
    final String TOTAL_CONV_NO_ACTION = "totalNoActionConversion";
    final String CONTROL_GROUP = "controlGroup";
    final String Z1_TARGET = "z1_target";

    Long totalNoActionConv = 0L;

    String journeyId = journey.getId();
    JourneyDef def = journey.getDef();

    // How many steps do we have for this journey?
    int stepCount = def.getStates().size();

    JourneySegmentStatsRecorder jssr = new JourneySegmentStatsRecorder(ctx);

    // -------------------------
    // We'll get the total number of population so far for the given journey
    // step ref.
    // This will be the total user added to that step ref.
    Journey.Step fromStep = new Journey.Step(journeyId, stepIndex);
    String fromStepId = fromStep.getRef();

    // If the step index passed is the last index, the conversion will always be
    // 0.
    // We are interested only for the step index less than the count
    if (stepIndex < (stepCount - 1))
    {
      Journey.Step toStep = new Journey.Step(journeyId, stepIndex + 1);
      String toStepId = toStep.getRef();
      totalNoActionConv = jssr.getConversionNumbersWithNoAction(journeyId,
          fromStepId, toStepId, changeLog.getId(), isMigrated,false); 
    }

    // -------------------------
    // Get the total population count and the conversion from the added and
    // deleted user counts for the given journey segment
    List<Map<String, Object>> populationInfos = null;

    populationInfos = jssr.getJourneyStatePopulation(journeyId, fromStepId,
        changeLog.getId(), isMigrated,false);

    Map<String, Object> populationInfo = !populationInfos.isEmpty()
        ? populationInfos.get(0)
        : null;
    Number deleted = populationInfo == null ? 0L
        : (Number) populationInfo.get("deleted");
    Number added = populationInfo == null ? 0L
        : (Number) populationInfo.get("added");
    Number reached = populationInfo == null ? 0L
        : (Number) populationInfo.get("reached");

    if (deleted == null) deleted = 0L;
    if (added == null) added = 0L;
    if (reached == null) reached = 0L;

    // # of people deleted is the number of people converted (we delete only
    // when people convert and move to the next step)
    Long totalConv = deleted.longValue();
    if (totalConv < 0L) totalConv = 0L;

    // # of people added is the total count of population in this step
    Long totalCnt = added.longValue();

    // It's not correct to take number of actions reached as target Cnt.
    // the same user can interact multiple times.

    // Target count is the # of people that we have reached.
    Long targetCnt = reached.longValue();

    // ---------------------
    // Prepare to send back
    Map<String, Object> item = new java.util.HashMap<>(3);
    item.put(TOTAL_CNT, totalCnt);
    item.put(TARGET_CNT, targetCnt);
    item.put(TOTAL_CONV, totalConv);
    item.put(TOTAL_CONV_NO_ACTION, totalNoActionConv);

    // If percent is 0, TI perhaps never been ran, so at least
    // report (100% - configured target number)
    int cg = 0;
    try
    {
      // if all stats calculate cg based on current z1_target value
      if (changeLog.getId().equals("*"))
      {
        cg = 0;
      }
      else
      {
        cg = 100 - Integer.parseInt(changeLog.getControlGroup());
      }
    }
    catch (NumberFormatException nfe)
    {
      ctx.getLogger(getClass()).warning(
          String.format("Check the definition of journey \"%s\", the control "
              + "group is set with a non-numeric data. We are assuming "
              + "control group to be 0.", journey.getName()));
    }
    item.put(CONTROL_GROUP, cg);

    return item;
  }
  /**
   * @param ctx
   * @param journey
   * @param stepIndex
   * @param changeLog
   * @param isMigrated
   * @param startDate
   * @param endDate
   * @return
   */
  private Map<String, Object> _populateActionStatItem(UContext ctx,
      Journey journey, int stepIndex, ExpChangeLog changeLog,
      boolean isMigrated, String startDate, String endDate)
  {
    if (changeLog == null) return new java.util.HashMap<String, Object>();

    final String TOTAL_CNT = "totalCount";
    final String TARGET_CNT = "targetCount";
    final String TOTAL_CONV = "totalConversion";
    final String TOTAL_CONV_NO_ACTION = "totalNoActionConversion";
    final String CONTROL_GROUP = "controlGroup";
    final String Z1_TARGET = "z1_target";

    Long totalNoActionConv = 0L;

    String journeyId = journey.getId();
    JourneyDef def = journey.getDef();

    // How many steps do we have for this journey?
    int stepCount = def.getStates().size();

    JourneySegmentStatsRecorder jssr = new JourneySegmentStatsRecorder(ctx);

    // -------------------------
    // We'll get the total number of population so far for the given journey
    // step ref.
    // This will be the total user added to that step ref.
    Journey.Step fromStep = new Journey.Step(journeyId, stepIndex);
    String fromStepId = fromStep.getRef();

    // If the step index passed is the last index, the conversion will always be
    // 0.
    // We are interested only for the step index less than the count
    if (stepIndex < (stepCount - 1))
    {
      Journey.Step toStep = new Journey.Step(journeyId, stepIndex + 1);
      String toStepId = toStep.getRef();
      totalNoActionConv = jssr.getConversionNumbersWithNoAction(journeyId,
          fromStepId, toStepId, changeLog.getId(), isMigrated, false, startDate,
          endDate);
    }

    // -------------------------
    // Get the total population count and the conversion from the added and
    // deleted user counts for the given journey segment
    List<Map<String, Object>> populationInfos = null;

    populationInfos = jssr.getJourneyStatePopulation(journeyId, fromStepId,
        changeLog.getId(), isMigrated, false, startDate, endDate);

    Map<String, Object> populationInfo = !populationInfos.isEmpty()
        ? populationInfos.get(0)
        : null;
    Number deleted = populationInfo == null ? 0L
        : (Number) populationInfo.get("deleted");
    Number added = populationInfo == null ? 0L
        : (Number) populationInfo.get("added");
    Number reached = populationInfo == null ? 0L
        : (Number) populationInfo.get("reached");

    if (deleted == null) deleted = 0L;
    if (added == null) added = 0L;
    if (reached == null) reached = 0L;

    // # of people deleted is the number of people converted (we delete only
    // when people convert and move to the next step)
    Long totalConv = deleted.longValue();
    if (totalConv < 0L) totalConv = 0L;

    // # of people added is the total count of population in this step
    Long totalCnt = added.longValue();

    // It's not correct to take number of actions reached as target Cnt.
    // the same user can interact multiple times.

    // Target count is the # of people that we have reached.
    Long targetCnt = reached.longValue();

    // ---------------------
    // Prepare to send back
    Map<String, Object> item = new java.util.HashMap<>(3);
    item.put(TOTAL_CNT, totalCnt);
    item.put(TARGET_CNT, targetCnt);
    item.put(TOTAL_CONV, totalConv);
    item.put(TOTAL_CONV_NO_ACTION, totalNoActionConv);

    // If percent is 0, TI perhaps never been ran, so at least
    // report (100% - configured target number)
    int cg = 0;
    try
    {
      cg = 100 - Integer.parseInt(changeLog.getControlGroup());
    }
    catch (NumberFormatException nfe)
    {
      ctx.getLogger(getClass()).warning(
          String.format("Check the definition of journey \"%s\", the control "
              + "group is set with a non-numeric data. We are assuming "
              + "control group to be 0.", journey.getName()));
    }
    item.put(CONTROL_GROUP, cg);

    return item;
  }

  // Merges the enter and convert maps to form the resultant segment data
  @SuppressWarnings("unchecked")
  private List<Map<String, Object>> _formCohortSegmentInsight(UContext ctx,
      Map<String, Object> mapEnter, Map<String, Object> mapConv)
  {
    List<Map<String, Object>> results = new ArrayList<>();
    for (Map.Entry<String, Object> entry : mapEnter.entrySet())
    {
      String segId = entry.getKey().split(",")[0];
      Segment segment = Segment.load(ctx, segId, true);
      if (segment == null) continue;
      String segName = segment.getDef().getName();
      Map<String, Object> mc = (Map<String, Object>) mapConv.remove(entry.getKey());
      Map<String, Object> me = (Map<String, Object>) entry.getValue();
      Map<String, Object> result = new HashMap<>();
      result.put("segmentId", segId);
      result.put("segmentName", segName);
      result.put("numEntries", me.get("frequency"));
      result.put("numConversions", (mc == null) ? 0L : mc.get("frequency"));
      if (me.get("time") != null) result.put("time", me.get("time"));
      results.add(result);
    }

    // The common segments have been taken care already , so add any entry that
    // are specific to convert
    for (Map.Entry<String, Object> entry : mapConv.entrySet())
    {
      String segId = entry.getKey().split(",")[0];
      Segment segment = Segment.load(ctx, segId, true);
      if (segment == null) continue;
      String segName = segment.getDef().getName();
      Map<String, Object> mc = (Map<String, Object>) entry.getValue();
      Map<String, Object> result = new HashMap<>();
      result.put("segmentId", segId);
      result.put("segmentName", segName);
      long l = (mc == null) ? 0L : ((Number) mc.get("frequency")).longValue();
      result.put("numEntries", 0L);
      result.put("numConversions", l);
      if (mc.get("time") != null) result.put("time", mc.get("time"));
      results.add(result);
    } 
    return results;
  }

  // .....................................................
  // Gets the message stat for a given content ID
  private Map<String, Object> _getDailyStatItems(UContext ctx,
      Map<String, Object> sentStat, Map<String, Object> interactedStat, Map<String, Object> dismissedStat)
  {
    final String CONTENT = "content";
    final String SENT = "sent";
    final String DISMISSED = "dismissed";
    final String INTERACTED = "interacted";

    Number sent = 0;
    Number dismissed = 0;
    Number interacted = 0;

    String actionName = null;
    if (sentStat != null)
    {
      actionName = (String) sentStat.get("actionName");
    }
    else if (interactedStat != null)
    {
      actionName = (String) interactedStat.get("actionName");
    }

    if (actionName == null) return null;

    // Only add the selected data. If a map is null put 0 instead.
    if ((sentStat != null) && !sentStat.isEmpty())
    {
      Object obj = sentStat.get("success");
      if (obj != null)
      {
        sent = (Number) obj;
      }
    } 
    if ((interactedStat != null) && !interactedStat.isEmpty())
    {
      Object obj = interactedStat.get("interacted");
      if (obj != null)
      {
        interacted = (Number) obj;
      }
    }
    if ((dismissedStat != null) && !dismissedStat.isEmpty())
    {
      Object obj = dismissedStat.get("dismissed");
      if (obj != null)
      {
        dismissed = (Number) obj;
      }
    }

    if (sent.longValue() == 0)
    {
      dismissed = 0;
      interacted = 0;
    }

    Map<String, Object> item = new java.util.HashMap<>(3);
    item.put(CONTENT, actionName);
    item.put(SENT, sent);
    item.put(INTERACTED, interacted);
    item.put(DISMISSED, dismissed);
    item.put("time", sentStat.get("time"));
    item.put("exeMode", sentStat.get("exeMode"));

    return item;
  }
  private static class ActionCountComparator implements Comparator<Map<String, Object>>
  {
    @Override
    public int compare(Map<String, Object> o1, Map<String, Object> o2)
    {
      Long aSent1 = (Long) o1.get(JourneySummaryInfo.aSent.name());
      Long aSent2 = (Long) o2.get(JourneySummaryInfo.aSent.name());

      return aSent2.compareTo(aSent1);
    }
  }
  
  /**
   * Populate action count from a journey
   * @param j
   * @param map
   */
  private void _populateActionCount(Journey j, Map<String, Object> map)
  {
    int count = 0;
    int pushCount = 0;

    JourneyDef jDef = j.getDef();
    List<StateDef> states = jDef.getStates();
    for (StateDef state : states)
    {
      List<ActivitySelectorDef> activities = state.getActivities();
      List<TimerDef> tds = state.getTimers();
      
      if (activities != null && !activities.isEmpty())
      {
        for (ActivitySelectorDef activity : activities)
        {
          List<ActionDef> actions = activity.getActions();
          count += actions.size();
          for (ActionDef act : actions)

          {
            TemplateConst.ActionType actionType = TemplateConst.ActionType
                .getByRefAndDimension(act.getRef(), act.getDimension());

            if (actionType == TemplateConst.ActionType.push) pushCount++;
          }
        }
      }
      else if (tds != null && !tds.isEmpty())
      {
        for (TimerDef td : tds)
        {
          List<ActionDef> actions = td.getActions();
          count += actions.size();
          for (ActionDef act : actions)

          {
            TemplateConst.ActionType actionType = TemplateConst.ActionType
                .getByRefAndDimension(act.getRef(), act.getDimension());

            if (actionType == TemplateConst.ActionType.push) pushCount++;
          }
        }
      }
    }
    map.put("actionCount", count);
    map.put("pushActionCount", pushCount);
  }

  // check if SE is enabled when calling journey endpoints
  private boolean checkIfEnableSE(UContext ctx, final HttpServletResponse resp) throws IOException
  {
    ResponseMessage msg = null;
    if (_journeyType == Type.c1 && !Journey.checkIfEnabledScheduledExperience())
    {
      msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
              ResponseMessage.Type.siDisabled,
              "Scheduled Experience is not Enabled!");
      resp.getWriter().print(msg);
      return false;
    }
    return true;
  }
}
