package com.z1.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.application.def.Application;
import udichi.core.application.def.Application.Artifact;
import udichi.core.application.def.ConfigDef;
import udichi.core.application.def.ConfigDef.Param;
import udichi.core.util.JaxbMarshaller;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import udichi.gateway.AppServiceFacade;
import udichi.gateway.AppServiceFacade.AppType;
import udichi.gateway.appservice.AppRegItem;
import udichi.gateway.defservice.DefinitionItem;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.AbstractSignal;
import z1.c3.Journey;
import z1.c3.Segment;
import z1.c3.SignalPart;
import z1.c3.def.ActivitySelectorDef;
import z1.c3.def.ParamsType;
import z1.c3.def.SignalDef;
import z1.c3.def.StateDef;
import z1.c3.def.TimerDef;
import z1.commons.Const;
import z1.commons.Const.TemplateCategory;
import z1.commons.def.RuleDef;
import z1.template.JourneyDesignUtils;
import z1.template.ModuleReaderFacade;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateConst.NSApp;
import z1.template.TemplateUtils;
import z1.users.User;

public class SignalsHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    all("c3/data/signals/all"),
    id("c3/data/signals/id"),
    categories("c3/data/signals/categories"),
    template("c3/data/signals/template"),
    templateAll("c3/data/signals/templateAll"),
    subscriptions("c3/data/signals/subscriptions"),
    references("c3/data/signals/references");

    String uri;

    GetCommand(String uri)
    {
      this.uri = uri;
    }

    public static Set<String> getUris()
    {
      return Arrays.stream(GetCommand.values()).map(cmd -> {
        return cmd.uri;
      }).collect(Collectors.toSet());
    }
  }

  private enum PostCommand
  {
    create,
    update,
    delete,
    enable, // remove event from eventBlackList
    disable, // add event to eventBlackList
    suspend, // suspend from runtime evaluation
    resume, // resume runtime evaluation
    subscribe,
    unsubscribe,
    subscribeOOTB,
    suspendTrigger,
    activateTrigger,
    ids,
    createWithAudience;
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/segments/<segment-id>
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          command = GetCommand.id;
        }

        switch (command)
        {
          // c3/data/signals/all?state=<state>&type=<req-type> => Sends all
          // signals
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);
            String state = req.getParameter("state");

            // We'll get all goal definitions to send them back
            List<SignalPart> sigs = null;
            sigs = SignalPart.forceLoadAll(ctx);
            String reqType = req.getParameter("type");
            for (SignalPart s : sigs)
            {
              if (s == null
                  || TemplateUtils.isTemplateConfig(s.getId(),
                      SignalPart.PREFIX)
                  || (null != state && !state.isEmpty()
                      && !s.getState().equals(state)))
              {
                continue;
              }

              SignalDef def = s.getDef();

              // Return only signals of this type. If type is specified
              if (reqType != null)
              {
                String types = def.getType(); // comma seperate types
                if (types == null || types.isEmpty()) continue;

                String[] splits = types.split(",");
                Set<String> typesSet = new HashSet<>();
                for (String type : splits)
                {
                  typesSet.add(type);
                }
                if (!typesSet.contains(reqType.toLowerCase()))
                {
                  continue;
                }
              }
              Map<String, Object> map = s.getDefAsMap();
              if (map != null)
              {
                map.put("id", s.getId());
                map.put("state", s.getState());

                DefinitionItem dItem = s.getDefItem();
                if (dItem != null && dItem.getValues() != null)
                {
                  map.put("lastUpdatedTime", (Long) dItem.getValues()
                      .get(DefinitionItem.Fields.lastUpdated.name()));
                  map.put("lastUpdatedBy", dItem.getValues()
                      .get(DefinitionItem.Fields.lastUpdatedBy.name()));
                  map.put("creationTime", (Long) dItem.getValues()
                      .get(DefinitionItem.Fields.timestamp.name()));
                  map.put("createdBy", dItem.getValues()
                      .get(DefinitionItem.Fields.owner.name()));
               
                  String inlineSegmentId = getInlineSegmentFromPayload2(s);
                  if (inlineSegmentId != null)
                    map.put("inlineSegmentId", inlineSegmentId);
                }

                ret.add(map);
              }
            }

            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);

            return;
          }

          // c3/data/signals/id?sId=<signal-id>&isC2=true
          case id:
          {
            // This must be a request for a specific goal payload.
            String sId = req.getParameter("sId");

            boolean isC2 = Boolean.parseBoolean(req.getParameter("isC2"));

            if (isC2)
            {
              Map<String, Object> respMap = new HashMap<>();
              SignalPart signal = SignalPart.load(ctx, sId, true);
              respMap.put("trigger", signal.getDef());
              String inlineSegmentId = getInlineSegmentFromPayload2(signal);
              if (inlineSegmentId != null)
              {
                Segment segment = Segment.load(ctx, inlineSegmentId, true);
                if (segment != null)
                {
                  respMap.put("inlineSegment", segment.getDef());
                }
              }
              String payload = new JsonMarshaller().serialize(respMap);
              resp.getWriter().print(payload);
              return;
            }

            // get the goal payload
            SignalPart s = SignalPart.load(ctx, sId, true);
            String payload = new JsonMarshaller().serialize(s.getDef());
            resp.getWriter().print(payload);

            return;
          }

          // c3/data/signals/references?sid=<signal-id>
          case references:
          {
            String sid = req.getParameter("sid");

            List<Object> result = _getReferences(ctx, sid);
            resp.getWriter().print(new JsonMarshaller().serialize(result));
            break;
          }

          // c3/data/signals/categories
          case categories:
          {
            TemplateCategory[] cats = Const.TemplateCategory.values();
            List<String> categories = new ArrayList<>(10);
            for (int i = 0; i < cats.length; i++)
            {
              categories.add(cats[i].id);
            }
            resp.getWriter().print(new JsonMarshaller().serialize(categories));
            return;
          }
          // returns all registered OOTB and subscribed templates.
          // If category is provided, returns only those registered under that
          // category.
          // c3/data/signals/templateAll[?category=<category>]
          case templateAll:
          {
            String category = req.getParameter("category");
            String mpNS = req.getParameter("ns");

            String version = "1.0";
            List<Map<String, Object>> templates = new ArrayList<>();
            List<Map<String, Object>> templatesOOTBS = new ArrayList<>();
            if (z1.commons.Utils.isMarketPlace)
            {
              String skipOOTB = req.getParameter("skipOOTB");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }

              AppServiceFacade asf = new AppServiceFacade(ctx);
              List<AppRegItem> subApps = asf.getSubscribedApps();

              // Go through the list of templates that had been subscribed
              for (AppRegItem subApp : subApps)
              {
                Map<String, Object> vals = subApp.getValues();
                if (vals.get("type") != null
                    && (vals.get("type").equals("trigger")))
                {
                  String appId = subApp.getId();

                  String pkgId = appId.split(":")[0];
                  if (skipOOTB != null && skipOOTB.equalsIgnoreCase("true")
                      && pkgId.equals(TemplateConst.TRIGGER_OOTB))
                  {
                    continue;
                  }
                  ModuleReaderFacade readerFacade = new ModuleReaderFacade(ctx,
                      pkgId, version);
                  // Load the app
                  String appDef = readerFacade.loadDefinition(appId);
                  if (appDef == null)
                  {
                    ctx.getLogger(getClass()).severe(
                        "Failed to load subscribed Trigger application - "
                            + appId);
                    continue;
                  }

                  Application app = JaxbMarshaller.toObject(appDef);

                  // Go through its artifacts and get only the signal artifacts
                  List<Artifact> artifacts = app.getArtifact();
                  for (Artifact artifact : artifacts)
                  {
                    if (!ArtifactType.config.name()
                        .equalsIgnoreCase(artifact.getType()))
                    {
                      continue;
                    }

                    String subtypeName = artifact.getSubtype();
                    if (!subtypeName.equals("signal"))
                    {
                      continue;
                    }

                    Map<String, Object> info = new HashMap<>(10);

                    String group = artifact.getGroup();
                    if (category != null && !category.equals(group))
                    {
                      continue;
                    }

                    String property = artifact.getProperty();
                    property = property.replaceAll("'", "\"");
                    Map<String, Object> propMap = new JsonMarshaller()
                        .readAsMap(property);

                    info.put("name", artifact.getId());
                    info.put("displayName", artifact.getName());
                    info.put(AppRegItem.Fields.description.name(),
                        artifact.getDescription());
                    info.put("iconCls", "");
                    info.put(AppRegItem.Fields.category.name(), group);
                    info.put(AppRegItem.Fields.type.name(),
                        AppType.trigger.name());
                    info.put(AppRegItem.Fields.state.name(),
                        propMap.get(AppRegItem.Fields.state.name()));
                    info.put(AppRegItem.Fields.publisherId.name(),
                        propMap.get(AppRegItem.Fields.publisherId.name()));

                    String imageStr = artifact.getImage();
                    List<Object> images = TemplateUtils
                        .convertObjectToList(imageStr);
                    info.put("images",
                        (new JsonMarshaller().serialize(images)));
                    if (pkgId.equals(TemplateConst.TRIGGER_OOTB))
                    {
                      templatesOOTBS.add(info);
                    }
                    else
                    {
                      templates.add(info);
                    }
                  }
                }
              }

            }
            else
            {
              boolean isSkipOOTBFromMP = false;
              AppServiceFacade asf = new AppServiceFacade(ctx);
              List<AppRegItem> subApps = asf.getSubscribedApps();

              // Go through the list of templates that had been subscribed
              for (AppRegItem subApp : subApps)
              {
                Map<String, Object> vals = subApp.getValues();
                if (vals.get("type") != null
                    && (vals.get("type").equals("trigger")))
                {
                  String appId = subApp.getId();

                  String pkgId = appId.split(":")[0];
                  if (pkgId.equals(TemplateConst.TRIGGER_OOTB))
                  {
                    isSkipOOTBFromMP = true;
                  }
                  // Load the app

                  ModuleReaderFacade readerFacade = new ModuleReaderFacade(ctx,
                      pkgId, version);
                  // Load the app
                  String appDef = readerFacade.loadDefinition(appId);
                  if (appDef == null)
                  {
                    ctx.getLogger(getClass()).severe(
                        "Failed to load subscribed Trigger application - "
                            + appId);
                    continue;
                  }

                  Application app = JaxbMarshaller.toObject(appDef);

                  // Go through its artifacts and get only the signal
                  // artifacts
                  List<Artifact> artifacts = app.getArtifact();
                  for (Artifact artifact : artifacts)
                  {
                    if (!ArtifactType.config.name()
                        .equalsIgnoreCase(artifact.getType()))
                    {
                      continue;
                    }

                    String subtypeName = artifact.getSubtype();
                    if (!subtypeName.equals("signal"))
                    {
                      continue;
                    }

                    Map<String, Object> info = new HashMap<>(10);

                    String group = artifact.getGroup();
                    if (category != null && !category.equals(group))
                    {
                      continue;
                    }

                    String property = artifact.getProperty();
                    property = property.replaceAll("'", "\"");
                    Map<String, Object> propMap = new JsonMarshaller()
                        .readAsMap(property);

                    info.put("name", artifact.getId());
                    info.put("displayName", artifact.getName());
                    info.put(AppRegItem.Fields.description.name(),
                        artifact.getDescription());
                    info.put("iconCls", "");
                    String images = artifact.getImage().replace("'", "\"");
                    info.put("images", images);
                    info.put(AppRegItem.Fields.category.name(), group);
                    info.put(AppRegItem.Fields.type.name(),
                        AppType.trigger.name());
                    info.put(AppRegItem.Fields.state.name(),
                        propMap.get(AppRegItem.Fields.state.name()));
                    info.put(AppRegItem.Fields.publisherId.name(),
                        propMap.get(AppRegItem.Fields.publisherId.name()));
                    if (pkgId.equals(TemplateConst.TRIGGER_OOTB))
                    {
                      templatesOOTBS.add(info);
                    }
                    else
                    {
                      templates.add(info);
                    }
                  }
                }
              }

              String BXMPNS = z1.commons.Utils.getBXMPNS(ctx);
              String BXMPApikey = z1.commons.Utils.getBXMPApikey(ctx);
              String response = null;
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                String endPoint = "/c3/data/signals/templateAll";
                if (category != null)
                {
                  endPoint += "?category=" + category;
                  if (isSkipOOTBFromMP)
                  {
                    endPoint += "&skipOOTB=true";
                  }
                }
                else
                {
                  if (isSkipOOTBFromMP)
                  {
                    endPoint += "?skipOOTB=true";
                  }
                }
                response = TemplateUtils.callMP(endPoint, null,
                    z1.commons.Utils.getBXMPNS(ctx), "GET",
                    z1.commons.Utils.getBXMPApikey(ctx));
              }
              if (response != null && !response.isEmpty() && !response
                  .contains("<title>Error 401 Unauthorized</title>"))
              {
                // currently mp and local can have subscribed trigger so trigger
                // definition can be duplicate.
                // After converting all trigger into modules , local will
                // contains only trigger modules which are in review
                // then duplicate entries will get eliminated.
                List<Map<String, Object>> matchesMP = new JsonMarshaller()
                    .readAsObject(response, ArrayList.class);
                if (!isSkipOOTBFromMP)
                {
                  templates = matchesMP;
                }
                else
                {
                  // older version does not evaluates skipOOTB parameter , if
                  // MP has older version then MP return OOTB template
                  // filter out OOTB templates, if already loaded from
                  // namespace
                  List<Map<String, Object>> filterModulesMP = matchesMP.stream()
                      .filter(match -> _isExistOOTBTrigger(match))
                      .collect(Collectors.toList());
                  templates = filterModulesMP;
                }

              }
            }
            // add triggerOOTB template at start of list so during rendering
            // these will get display at start
            if (!templatesOOTBS.isEmpty())
            {
              templates.addAll(0, templatesOOTBS);
            }
            String payload = new JsonMarshaller().serialize(templates);
            resp.getWriter().print(payload);
            return;
          }
          // returns the template configuration for signal and any configs needs
          // by it.
          // c3/data/signals/template?templateId=<templateId>
          case template:
          {
            Map<String, Object> info = new HashMap<>(10);
            String templateId = req.getParameter("templateId"); // e.g:
            String version = "1.0";
            String modId = templateId; // udc.system.core.TriggerOOTB:__Z1_TemperatureBreach
            JsonMarshaller jm = new JsonMarshaller();
            if (templateId == null)
            {
              info.put("message", "Missing template ID for loading.");
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }

            String[] idParts = templateId.split(":");
            if (idParts.length < 2)
            {
              info.put("message",
                  "Template ID '" + templateId + "' is malformed");
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }
            String pkgId = idParts[0];
            String appId = pkgId + ":" + version;
            if (pkgId == null)
            {
              info.put("message", "App ID is missing");
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }
            if (z1.commons.Utils.isMarketPlace)
            {
              if (null == ctx.getNamespace())
              {
                ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
              }
              // Load the app container for this templateId

              ModuleReaderFacade readerFacade = new ModuleReaderFacade(ctx,
                  pkgId, version);
              // Load the app
              String appDef = readerFacade.getArtifactPayloads(appId,
                  templateId);
              if (appDef == null)
              {
                ctx.getLogger(getClass())
                    .severe("Failed to load application - '" + pkgId
                        + "' for template '" + templateId + "'");
                info.put("message", "Failed to load application - '" + pkgId
                    + "' for template '" + templateId + "'");
                resp.getWriter().print(new JsonMarshaller().serialize(info));
                return;
              }

              Map<String, Object> defMap = jm.readAsMap(appDef);
              List<Map<String, Object>> artifacts = (List<Map<String, Object>>) defMap
                  .get(ArtifactType.config.name());
              for (Map<String, Object> artifact : artifacts)
              {
                String subtypeName = (String) artifact.get("subtype");
                if (subtypeName.equals("signal")
                    && modId.equals(artifact.get("id").toString()))
                {
                  String payload = (String) artifact.get("payload");
                  resp.getWriter().print(payload);
                  break;
                }
              }
            }
            else
            {
              // pkgId is namespace appId or trigger ootb
              // then no need to call mp
              boolean isNSApp = NSApp.getAppIds().contains(pkgId);
              if (!isNSApp)
              {
                // pkgId is triggerOOTB then first read it from local
                // if appDef is not present in local read it from MP
                String appDef = null;
                if (TemplateConst.TRIGGER_OOTB.equals(pkgId))
                {
                  // Load the app container for this templateId

                  ModuleReaderFacade readerFacade = new ModuleReaderFacade(ctx,
                      pkgId, version);
                  // Load the app
                  appDef = readerFacade.getArtifactPayloads(appId, templateId);
                  if (appDef != null)
                  {

                    Map<String, Object> defMap = jm.readAsMap(appDef);
                    List<Map<String, Object>> artifacts = (List<Map<String, Object>>) defMap
                        .get(ArtifactType.config.name());
                    for (Map<String, Object> artifact : artifacts)
                    {
                      String subtypeName = (String) artifact.get("subtype");
                      if (subtypeName.equals("signal")
                          && modId.equals(artifact.get("id").toString()))
                      {
                        String payload = (String) artifact.get("payload");
                        resp.getWriter().print(payload);
                        break;
                      }
                    }
                  }
                }
                if (appDef == null)
                {
                  String BXMPNS = z1.commons.Utils.getBXMPNS(ctx);
                  String BXMPApikey = z1.commons.Utils.getBXMPApikey(ctx);
                  String response = null;
                  if (templateId != null)
                  {
                    if (BXMPNS != null && !BXMPNS.isEmpty()
                        && BXMPApikey != null && !BXMPApikey.isEmpty())
                    {
                      response = TemplateUtils.callMP(
                          "/c3/data/signals/template?templateId=" + templateId,
                          null, BXMPNS, "GET", BXMPApikey);
                    }
                  }
                  if (response == null)
                  {
                    ctx.getLogger(getClass())
                        .severe("Failed to load application - '" + pkgId
                            + "' for template '" + templateId + "'");
                    info.put("message", "Failed to load application - '" + pkgId
                        + "' for template '" + templateId + "'");
                    resp.getWriter()
                        .print(new JsonMarshaller().serialize(info));
                    return;
                  }
                  else
                  {
                    resp.getWriter().print(response);
                  }
                }
              }
              else
              {
                // Load the app container for this templateId

                ModuleReaderFacade readerFacade = new ModuleReaderFacade(ctx,
                    pkgId, version);
                // Load the app
                String appDef = readerFacade.getArtifactPayloads(pkgId,
                    templateId);
                if (appDef == null)
                {
                  ctx.getLogger(getClass())
                      .severe("Failed to load application - '" + pkgId
                          + "' for template '" + templateId + "'");
                  info.put("message", "Failed to load application - '" + pkgId
                      + "' for template '" + templateId + "'");
                  resp.getWriter().print(new JsonMarshaller().serialize(info));
                  return;
                }

                Map<String, Object> defMap = jm.readAsMap(appDef);
                List<Map<String, Object>> artifacts = (List<Map<String, Object>>) defMap
                    .get(ArtifactType.config.name());
                for (Map<String, Object> artifact : artifacts)
                {
                  String subtypeName = (String) artifact.get("subtype");
                  if (subtypeName.equals("signal")
                      && modId.equals(artifact.get("id").toString()))
                  {
                    String payload = (String) artifact.get("payload");
                    resp.getWriter().print(payload);
                    break;
                  }
                }
              }
            }
            return;
          }
          // returns all non-OOTB (subscription required) templates.
          // c3/data/signals/subscriptions
          case subscriptions:
          {
            AppType appType = AppType.trigger;

            AppServiceFacade asf = new AppServiceFacade(ctx);

            List<Map<String, Object>> subscriptions = new ArrayList<>(10);

            List<String> subscribedApps = new ArrayList<>(10);

            // First, find all the ones that had been subscribed
            List<AppRegItem> subApps = asf.getSubscribedApps();
            for (AppRegItem subApp : subApps)
            {
              Map<String, Object> vals = subApp.getValues();

              String appId = subApp.getId();
              // TODO: Use 'tag' for now for templateType. 'type' from app's
              // values is not set. Why?
              String templateType = (String) vals
                  .get(AppRegItem.Fields.tag.name());
              if (templateType == null
                  || !templateType.equals(AppType.trigger.name())
                  || appId.equals(TemplateConst.TRIGGER_OOTB))
              {
                // Don't count anything that's an OOTB or not a trigger
                continue;
              }

              String appDef = Utils.loadDefinition(ctx, appId,
                  ArtifactType.application);
              if (appDef != null)
              {
                subscribedApps.add(appId);

                Application app = JaxbMarshaller.toObject(appDef);

                // Go through its artifacts and get only the signal artifacts
                List<Artifact> artifacts = app.getArtifact();

                List<Map<String, Object>> templates = new ArrayList<>(10);

                for (Artifact artifact : artifacts)
                {
                  if (!ArtifactType.config.name()
                      .equalsIgnoreCase(artifact.getType()))
                  {
                    continue;
                  }

                  String subtypeName = artifact.getSubtype();
                  if (!subtypeName.equals("signal"))
                  {
                    continue;
                  }

                  Map<String, Object> info = new HashMap<String, Object>();

                  String group = artifact.getGroup();

                  String property = artifact.getProperty();
                  property = property.replaceAll("'", "\"");
                  Map<String, Object> propMap = (Map<String, Object>) new JsonMarshaller()
                      .readAsMap(property);

                  info.put("name", artifact.getId());
                  info.put("displayName", artifact.getName());
                  info.put(AppRegItem.Fields.description.name(),
                      artifact.getDescription());
                  info.put("iconCls", artifact.getImage());
                  info.put(AppRegItem.Fields.category.name(), group);
                  info.put(AppRegItem.Fields.type.name(),
                      AppType.trigger.name());
                  info.put(AppRegItem.Fields.state.name(),
                      propMap.get(AppRegItem.Fields.state.name()));
                  info.put(AppRegItem.Fields.publisherId.name(),
                      propMap.get(AppRegItem.Fields.publisherId.name()));

                  templates.add(info);
                }

                Map<String, Object> subscription = new HashMap<String, Object>();
                subscription.put("id", appId);
                subscription.put("isSubscribed", "true");
                subscription.put("templates", templates);
                subscriptions.add(subscription);
              }
            }

            // Now go through the list of published apps
            List<AppRegItem> pubApps = asf.getPublishedApps(appType);
            for (AppRegItem pubApp : pubApps)
            {
              Map<String, Object> vals = pubApp.getValues();

              String appId = pubApp.getId();
              // TODO: Use 'tag' for now for templateType. 'type' from app's
              // values is not set. Why?
              String templateType = (String) vals
                  .get(AppRegItem.Fields.tag.name());
              if (templateType == null
                  || !templateType.equals(AppType.trigger.name())
                  || appId.equals(TemplateConst.TRIGGER_OOTB)
                  || subscribedApps.contains(appId))
              {
                // Don't count anything that's an OOTB or not a trigger or had
                // been subscribed
                continue;
              }

              // If reach here, it has not been subscribed
              String appDef = Utils.loadDefinition(ctx, appId,
                  ArtifactType.application);
              if (appDef != null)
              {
                Application app = JaxbMarshaller.toObject(appDef);

                // Go through its artifacts and get only the signal artifacts
                List<Artifact> artifacts = app.getArtifact();

                List<Map<String, Object>> templates = new ArrayList<>(10);

                for (Artifact artifact : artifacts)
                {
                  if (!ArtifactType.config.name()
                      .equalsIgnoreCase(artifact.getType()))
                  {
                    continue;
                  }

                  String subtypeName = artifact.getSubtype();
                  if (!subtypeName.equals("signal"))
                  {
                    continue;
                  }

                  Map<String, Object> info = new HashMap<String, Object>();

                  String group = artifact.getGroup();

                  String property = artifact.getProperty();
                  property = property.replaceAll("'", "\"");
                  Map<String, Object> propMap = (Map<String, Object>) new JsonMarshaller()
                      .readAsMap(property);

                  info.put("name", artifact.getId());
                  info.put("displayName", artifact.getName());
                  info.put(AppRegItem.Fields.description.name(),
                      artifact.getDescription());
                  info.put("iconCls", artifact.getImage());
                  info.put(AppRegItem.Fields.category.name(), group);
                  info.put(AppRegItem.Fields.type.name(),
                      AppType.trigger.name());
                  info.put(AppRegItem.Fields.state.name(),
                      propMap.get(AppRegItem.Fields.state.name()));
                  info.put(AppRegItem.Fields.publisherId.name(),
                      propMap.get(AppRegItem.Fields.publisherId.name()));

                  templates.add(info);
                }

                Map<String, Object> subscription = new HashMap<>(10);
                subscription.put("id", appId);
                subscription.put("isSubscribed", "false");
                subscription.put("templates", templates);
                subscriptions.add(subscription);
              }
            }

            String payload = new JsonMarshaller().serialize(subscriptions);
            resp.getWriter().print(payload);
            return;
          }
        }
      }

      private boolean _isExistOOTBTrigger(Map<String, Object> match)
      {
        String name = match.get("name").toString();
        String pkgId = name.split(":")[0];
        return !pkgId.equals(TemplateConst.TRIGGER_OOTB);
      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          case create:
          case update:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            boolean isUpdate = false;
            String sid = req.getParameter("sid");
            boolean isC2 = Boolean.parseBoolean(req.getParameter("isC2"));

            ResponseMessage respMsg = null;
            if (sid != null)
            {
              isUpdate = true;
            }

            // Retrieve SignalDef payload from the request
            String payload = ServletUtil.getPayload(req);
            if (payload == null) return;
            Map<String, Object> signalPlMap = new JsonMarshaller()
                .readAsMap(payload);
            if (signalPlMap == null || signalPlMap.isEmpty()) return;

            // Get signal name to use as name suffix for supportive artifacts.
            // That way the supportive artifacts are uniquely identified for
            // this Signal.
            // Remove all white spaces from name.
            String sname = ((String) signalPlMap.get("name")).replaceAll("\\s+",
                "");

            // Prepare the Signal config but don't save it yet until the end.
            SignalPart s = (isUpdate) ? SignalPart.load(ctx, sid, true)
                : SignalPart.create(ctx);

            // If it's an update, delete all of its supportive artifacts first
            // then recreate
            if (isUpdate)
            {
              _deleteTemplateSupportArtifacts(ctx, s);
            }

            s.setPayload(payload);
            if (isC2)
            {
              s.save();
              sid = s.getId();

              resp.getWriter().print("{ \"id\": \"" + sid + "\" }");
              break;
            }
            SignalDef def = s.getDef();

            // This is the list of params from template SignalDef
            List<ParamsType> defParams = def.getParams();
            // This list hold params with values entered by user
            List<ParamsType> sharedParams = new ArrayList<ParamsType>();
            List<RuleDef> rules = def.getRule();
            for (ParamsType param : defParams)
            {
              String pName = param.getName();
              for (RuleDef rule : rules)
              {
                if (rule.getField().equals(pName))
                {
                  ParamsType sharedParam = new ParamsType();
                  sharedParam.setName(pName);
                  sharedParam.setValue(rule.getValue());
                  sharedParams.add(sharedParam);
                  break;
                }
              }
            }

            // Add signal name to params to use as name suffix for supportive
            // artifacts.
            // That way the supportive artifacts are uniquely identified for
            // this Signal
            ParamsType snameParam = new ParamsType();
            snameParam.setName(TemplateConst.PARAM_SIGNAL_NAME);
            snameParam.setValue(sname);
            sharedParams.add(snameParam);

            // Get trigger appId
            String type = def.getType();
            String version = "1.0";
            String appId = null;
            String pkgId = null;
            String scope = null;
            if (type != null && type.indexOf(':') >= 0)
            {
              String[] idParts = type.split(":");
              if (idParts.length == 2)
              {
                pkgId = idParts[0];
                scope = idParts[1] + ":" + version;
              }
            }

            if (pkgId == null)
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.triggerCreateOrUpdateFailed,
                  "Cannot find trigger appId.");
              resp.getWriter().print(respMsg.toString());
              return;
            }

            appId = pkgId+":"+version;
            String modId = type + ":" + version;
            boolean foundPublishedTrigger = true;
            // First try MP
            String BXMPNS = z1.commons.Utils.getBXMPNS(ctx);
            String BXMPApikey = z1.commons.Utils.getBXMPApikey(ctx);
            String appDef = null;
            if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                && !BXMPApikey.isEmpty())
            {
              appDef = TemplateUtils.callMP(
                  "/c3/data/modules/loadDefinition?id=" + appId, null, BXMPNS,
                  "GET", BXMPApikey);
            }
            if (appDef == null || appDef.contains("\"status\": \"fail\""))
            {
              foundPublishedTrigger = false;
              // Try local
              ModuleReaderFacade mrf = new ModuleReaderFacade(ctx, pkgId,
                  version);
              appDef = mrf.loadDefinition(appId);

              if (appDef == null)
              {
                respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.triggerCreateOrUpdateFailed,
                    "Unable to find any app container on Market Place or local for this trigger.");
                resp.getWriter().print(respMsg.toString());
                return;
              }
            }

            // loop through the app's artifacts to find supportive artifacts to
            // create
            Application app = JaxbMarshaller.toObject(appDef);
            List<Artifact> artifacts = app.getArtifact();

            Map<String, Object> arts = new HashMap<>();
            JsonMarshaller jm = new JsonMarshaller();
            if (foundPublishedTrigger)
            {
              String defs = null;
              // Call to MP to check on published module first
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                defs = TemplateUtils.callMP(
                    "/c3/data/modules/loadArtifacts?id=" + modId, null, BXMPNS,
                    "GET", BXMPApikey);
              }
              if (defs == null)
              {
                defs = TemplateUtils.getArtifacts(ctx, modId, "", false, null);
              }
              if (defs == null)
              {
                respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.triggerCreateOrUpdateFailed,
                    "Unable to load artifacts defs for trigger - " + pkgId);
                resp.getWriter().print(respMsg.toString());
                return;
              }

              arts = new JsonMarshaller().readAsMap(defs);
            }

            ModuleVisitor mv = new ModuleVisitor(ctx, app,
                ModuleVisitor.VisitingOperation.create);

            Set<String> subtypes = new HashSet<>();

            for (Artifact artifact : artifacts)
            {
              String artId = artifact.getId();
              String artPL = null;
              if (foundPublishedTrigger)
              {
                List<Map<String, Object>> artList = (List<Map<String, Object>>) arts
                    .get(artifact.getType());
                for (Map<String, Object> art : artList)
                {
                  String id = (String) art.get("id");
                  if (!id.equals(artId)) continue;
                  if (artifact.getSubtype().equals("entity"))
                  {
                    artPL = jm
                        .serialize(jm.readAsMap(art.get("payload").toString())
                            .get("payload"));
                  }
                  else
                  {
                    artPL = art.get("payload").toString();
                  }
                  break;
                }
              }

              if (!ArtifactType.config.name()
                  .equalsIgnoreCase(artifact.getType()))
              {
                continue;
              }
              if (!artifact.getScope().equals(scope))
              {
                continue;
              }
              if (artifact.getSubtype().equals("signal"))
              {
                continue;
              }
              String subtypeName = artifact.getSubtype();

              ConfigDef configDef = app.getConfig();
              List<Param> configParams = (configDef == null) ? null
                  : configDef.getParam();
              List<ParamsType> gParams = TemplateUtils
                  .getGlobalParams(configParams);
              TemplateArtifact ta = new TemplateArtifact(ctx, app.getId(),
                  artifact, sharedParams, gParams, subtypeName, artPL, false,
                  version);
              ta.accept(mv);

              List<Object> typeList = (List<Object>) arts
                  .get(ta.getType().name());
              if (typeList == null)
              {
                typeList = new ArrayList<>();
                arts.put(ta.getType().name(), typeList);
              }
              Map<String, Object> artInfo = new HashMap<>();
              artInfo.put("subtype", artifact.getSubtype());
              artInfo.put("id", artifact.getId());
              artInfo.put("payload", mv.getVisitPayload());
              typeList.add(artInfo);
              subtypes.add(artifact.getSubtype());
            }

            // String res = new JsonMarshaller().serialize(arts);
            // resp.getWriter().print(res);

            // Set signal config's payload2 with references to supportive
            // artifacts, and enable the channel (if any)

            // Get support artifact IDs from visitor's payload
            // extract required attribute for payload2
            Set<String> keySet = new HashSet<>(Arrays.asList("artifactId",
                "configId", "subtype", "deletable"));
            Map<String, Object> visitorPl = mv.getPayload();
            visitorPl.forEach((k, v) -> {
              ((List<Object>) v).forEach(
                  pl -> ((Map<String, Object>) pl).keySet().retainAll(keySet));
            });

            // set createdBy attribute in payload2
            if (command.equals(PostCommand.create))
            {
              visitorPl.put("createdBy",
                  ctx.getUser().getValues().get(User.ROLE));
              s.setPayload2(jm.serialize(visitorPl));
            }

            // Now save the signal config and return its ID
            s.save();
            sid = s.getId();

            resp.getWriter().print("{ \"id\": \"" + sid + "\" }");
            TemplateUtils.clearModuleCache(ctx, subtypes);
            break;
          }
          // c3/data/signals/delete?sid=<signal-id>
          case delete:
          {
            String[] subParts = new String[2];
            req.setAttribute("id", req.getParameter("sid"));
            req.setAttribute("type", "signal");
            subParts[0] = "signal";
            subParts[1] = cStr;

            new BXHandler().post().handle(ctx, subParts, req, resp);
            break;
          }

          // c3/data/signals/<suspend|resume>?sid=<signal-id>
          case suspend:
          case resume:
          {
            String sid = req.getParameter("sid");
            String[] idType = sid.split("\\+");
            String subtype = idType[0];

            SignalPart s = SignalPart.load(ctx, sid, true);
            String payload2 = s.getPayload2();
            Map<String, Object> ret = TemplateUtils
                .getModuleConfigInstanceReferences(ctx, payload2);
            if (ret == null || !ret.containsKey("type")) return;
            TemplateConst.InstanceConfigType ict = TemplateConst.InstanceConfigType
                .valueOf((String) ret.get("type"));
            Set<String> refs = (Set<String>) ret.get("refs");

            ResponseMessage msg = new ResponseMessage(ctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            ;
            if (ict.equals(TemplateConst.InstanceConfigType.primary))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "This trigger is primary component of a module instance. Operation '"
                        + command.name()
                        + "' finished and was also applied on its supportive components: "
                        + references);
              }

            }
            else if (ict.equals(TemplateConst.InstanceConfigType.supportive))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "This trigger is supportive config instance and still being referenced in: "
                        + references);
                resp.getWriter().print(msg.toString());
                return;
              }

            }

            Artifact art = new Artifact();
            art.setSubtype(subtype);
            art.setId(sid);
            TemplateArtifact ta = new TemplateArtifact(ctx, null, art, null,
                null, subtype, null);

            ModuleVisitor.VisitingOperation mvOp;
            if (command.equals(PostCommand.suspend))
            {
              mvOp = ModuleVisitor.VisitingOperation.suspend;
            }
            else
            {
              mvOp = ModuleVisitor.VisitingOperation.resume;
            }

            ModuleVisitor mv = new ModuleVisitor(ctx, null, mvOp);

            mv.visit(ta);
            msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            resp.getWriter().print(msg.toString());
            break;
          }

          // c3/data/signals/enable?eventName=<event-name>
          // c3/data/signals/disable?eventName=<event-name>
          case enable:
          case disable:
          {
            String eventName = req.getParameter("eventName");

            AbstractSignal.blacklistEvent(ctx, eventName,
                command.equals(PostCommand.disable));
            if (command.equals(PostCommand.disable))
            {
              ArtifactAudit.newInstance(ctx, ItemTypes.events, eventName,
                  eventName, Operations.disable).save();
            }
            else
            {
              ArtifactAudit.newInstance(ctx, ItemTypes.events, eventName,
                  eventName, Operations.enable).save();
            }
            break;
          }

          // c3/data/signals/subscribe?templateAppId=<templateAppId>
          case subscribe:
          {
            Map<String, Object> info = new HashMap<>(10);
            String templateAppId = req.getParameter("templateAppId");

            if (templateAppId == null)
            {
              info.put("message", "Missing template ID for subscription.");
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }

            // e.g: // udc.system.core.Template_Example

            AppServiceFacade asf = new AppServiceFacade(ctx);
            // Go through the list of triggers that had been subscribed
            // and check if this is been subscribed.
            boolean isSubscribed = false;
            List<AppRegItem> subApps = asf.getSubscribedApps();
            for (AppRegItem subApp : subApps)
            {
              if (subApp.getId().equals(templateAppId))
              {
                isSubscribed = true;
                break;
              }
            }

            if (isSubscribed)
            {
              boolean hasReference = false;
              // Check references
              // We'll get all goal definitions to send them back
              List<SignalPart> sigs = SignalPart.forceLoadAll(ctx);
              for (SignalPart s : sigs)
              {
                if (s == null) continue;
                SignalDef def = s.getDef();

                String type = def.getType();

                // Check if this is an instance of a template trigger
                if (type != null && type.startsWith(templateAppId + ":"))
                {
                  hasReference = true;
                  break;
                }
              }

              if (hasReference)
              {
                String message = "There's one or more triggers instance referencing template(s) in App '"
                    + templateAppId
                    + "'. Resubscribing brings latest version of this App and may cause unexpected behavior for its references."
                    + " Delete all references first before resubscribe.";
                info.put("message", message);
                resp.getWriter().print(new JsonMarshaller().serialize(info));
                return;
              }

              // No reference, so unsubscribe first.
              asf.unsubscribeApp(templateAppId);
            }

            AppType appType = AppType.trigger;

            String description = null;
            List<AppRegItem> pubApps = asf.getPublishedApps(appType);
            for (AppRegItem pubApp : pubApps)
            {
              Map<String, Object> vals = pubApp.getValues();
              String appId = pubApp.getId();
              // TODO: Use 'tag' for now for templateType. 'type' from app's
              // values is not set. Why?
              String templateType = (String) vals
                  .get(AppRegItem.Fields.tag.name());
              if (!appId.equals(templateAppId) || templateType == null
                  || !templateType.equals(AppType.trigger.name()))
              {
                // Don't subscribe anything that's not a trigger
                continue;
              }

              description = (String) vals
                  .get(AppRegItem.Fields.description.name());
              break;
            }

            asf.subscribeApp(templateAppId, description);

            Map<String, Object> subscription = new HashMap<String, Object>();

            // templateAppId is now subscribed. return list of its artifacts
            String appDef = Utils.loadDefinition(ctx, templateAppId,
                ArtifactType.application);
            if (appDef != null)
            {
              Application app = JaxbMarshaller.toObject(appDef);

              // Go through its artifacts and get only the signal artifacts
              List<Artifact> artifacts = app.getArtifact();

              List<Map<String, Object>> templates = new ArrayList<Map<String, Object>>();

              for (Artifact artifact : artifacts)
              {
                if (!ArtifactType.config.name()
                    .equalsIgnoreCase(artifact.getType()))
                {
                  continue;
                }

                String subtypeName = artifact.getSubtype();
                if (!subtypeName.equals("signal"))
                {
                  continue;
                }

                info = new HashMap<String, Object>();

                String group = artifact.getGroup();

                String property = artifact.getProperty();
                property = property.replaceAll("'", "\"");
                Map<String, Object> propMap = (Map<String, Object>) new JsonMarshaller()
                    .readAsMap(property);

                info.put("name", artifact.getId());
                info.put("displayName", artifact.getName());
                info.put(AppRegItem.Fields.description.name(),
                    artifact.getDescription());
                info.put("iconCls", artifact.getImage());
                info.put(AppRegItem.Fields.category.name(), group);
                info.put(AppRegItem.Fields.type.name(), AppType.trigger.name());
                info.put(AppRegItem.Fields.state.name(),
                    propMap.get(AppRegItem.Fields.state.name()));
                info.put(AppRegItem.Fields.publisherId.name(),
                    propMap.get(AppRegItem.Fields.publisherId.name()));

                templates.add(info);
              }

              subscription.put("id", templateAppId);
              subscription.put("isSubscribed", "true");
              subscription.put("templates", templates);
            }

            String payload = new JsonMarshaller().serialize(subscription);
            resp.getWriter().print(payload);
            return;
          }
          // c3/data/signals/unsubscribe?templateAppId=<templateAppId>
          case unsubscribe:
          {
            Map<String, Object> info = new HashMap<>(10);
            String templateAppId = req.getParameter("templateAppId");

            if (templateAppId == null)
            {
              info.put("message", "Missing template ID for unsubscription.");
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }

            // e.g: udc.system.core.Template_Example

            AppServiceFacade asf = new AppServiceFacade(ctx);

            // Go through the list of Apps that had been subscribed
            // and check if this is been subscribed.
            boolean isSubscribed = false;
            List<AppRegItem> subApps = asf.getSubscribedApps();
            for (AppRegItem subApp : subApps)
            {
              if (subApp.getId().equals(templateAppId))
              {
                isSubscribed = true;
                break;
              }
            }

            if (!isSubscribed)
            {
              info.put("message",
                  "'" + templateAppId + "' has not been subscribed.");
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }

            boolean hasReference = false;
            // Check references
            // We'll get all goal definitions to send them back
            List<SignalPart> sigs = SignalPart.forceLoadAll(ctx);
            for (SignalPart s : sigs)
            {
              if (s == null) continue;
              SignalDef def = s.getDef();

              String type = def.getType();

              // Check if this is an instance of a template trigger
              if (type != null && type.startsWith(templateAppId + ":"))
              {
                hasReference = true;
                break;
              }
            }

            if (hasReference)
            {
              String message = "There's one or more trigger instances referencing template(s) from App '"
                  + templateAppId
                  + "'. If unsubscribe now then resubscribe later will bring latest version of this template and may cause unexpected behavior for its references."
                  + " Delete all references first before unsubscribe.";
              info.put("message", message);
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }

            asf.unsubscribeApp(templateAppId);
            return;
          }
          // c3/data/signals/subscribeOOTB
          case subscribeOOTB:
          {
            // do nothing and simply return
            // TODO: Trigger screen in Dev menu should not call to this anymore.
            return;
          }
          
          // c3/data/signals/ids
          case ids:
          {
            String reqPl = ServletUtil.getPayload(req);
            if (reqPl == null || reqPl.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Missing request payload.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            JsonMarshaller jm = new JsonMarshaller();
            List<String> sids =jm.readAsObject(reqPl, List.class);
            List<Map<String,Object>> ret = new ArrayList<>();
            sids.forEach(sid -> {
              SignalPart s = SignalPart.load(ctx, sid, true);
              if (s != null)
              {
                Map<String, Object> map = s.getDefAsMap();
                if (map != null)
                {
                  map.put("id", s.getId());
                  map.put("state", s.getState());

                  DefinitionItem dItem = s.getDefItem();
                  if (dItem != null && dItem.getValues() != null)
                  {
                    map.put("lastUpdatedTime", (Long) dItem.getValues()
                        .get(DefinitionItem.Fields.lastUpdated.name()));
                    map.put("lastUpdatedBy", dItem.getValues()
                        .get(DefinitionItem.Fields.lastUpdatedBy.name()));
                    map.put("creationTime", (Long) dItem.getValues()
                        .get(DefinitionItem.Fields.timestamp.name()));
                    map.put("createdBy", dItem.getValues()
                        .get(DefinitionItem.Fields.owner.name()));
                  }

                  ret.add(map);
                }
              }
            });

            resp.getWriter().print(jm.serialize(ret));

            return;
          }

          // c3/data/signals/createWithAudience
          case createWithAudience:
          {
            String reqPl = ServletUtil.getPayload(req);
            if (reqPl.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Missing request payload.");
              resp.getWriter().print(msg.toString());
              return;
            }

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> reqMap = jm.readAsMap(reqPl);
            String refAudience = String.valueOf(reqMap.get("refAudience"));
            Segment audience = Segment.load(ctx, refAudience);

            if (audience != null && !audience.isAudienceSegment())
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Reference audience id is not audience.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, String> respMap = new HashMap<>();
            String triggerName = null;

            Map<String, Object> trigger = null;
            if (reqMap != null && reqMap.get("trigger") instanceof Map)
            {
              trigger = (Map<String, Object>) reqMap.get("trigger");
            }

            Map<String, Object> inlineSegment = null;
            if (reqMap != null && reqMap.get("inlineSegment") instanceof Map)
            {
              inlineSegment = (Map<String, Object>) reqMap.get("inlineSegment");
            }

            if (trigger != null)
            {
              triggerName = String.valueOf(trigger.get("name"));
              if (SignalPart.isSignalExistWithName(ctx, triggerName))
              {
                ResponseMessage msg = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Trigger name already exists.");
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            if (inlineSegment != null)
            {
              String inlineSegmentName = String
                  .valueOf(inlineSegment.get("name"));
              if (triggerName == null)
              {
                triggerName = inlineSegmentName;
              }
              else
              {
                if (!triggerName.equals(inlineSegmentName))
                {
                  ResponseMessage msg = new ResponseMessage(ctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "Trigger name and inline segment name doesn't matches. Name should be same in payload");
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
            }

            if (audience != null && inlineSegment != null)
            {
              Set<String> audFields = audience.getDef().getRule().stream()
                  .map(ruleDef -> ruleDef.getField())
                  .collect(Collectors.toSet());

              List<Map<String, Object>> rules = (List<Map<String, Object>>) inlineSegment
                  .get("rule");
              Set<String> segFields = rules.stream().map(rule -> {
                return String.valueOf(rule.get("field"));
              }).collect(Collectors.toSet());

              int validationCode = JourneyDesignUtils.validateRules(true,
                  audFields, segFields, audience.hasCustomEppRules());

              if (validationCode != 0)
              {
                ResponseMessage msg = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams, JourneyDesignUtils
                        .generateFieldConflictErrorMsg(validationCode));
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            if (inlineSegment != null)
            {
              Segment s = Segment.create(ctx);
              s.setPayload(jm.serialize(inlineSegment));
              s.save();
              respMap.put("inlineSegmentId", s.getId());
            }
            
            if (trigger != null)
            {
              SignalPart s = SignalPart.create(ctx);
              List<Object> rules = (List<Object>) trigger.get("rule");
              rules.stream().filter(Map.class::isInstance).map(Map.class::cast)
                  .filter(rule -> "event".equals(rule.get("field"))).findFirst()
                  .ifPresent(rule -> rule.put("value",
                      rule.get("value") + "|" + Const.OOTB_AUTO_SCORE_EVENT));
              RuleDef ruleDef = new RuleDef();
              ruleDef.setField(Const.Z1_AUTO_SCORE_EVAL);
              ruleDef.setOperator("==");
              ruleDef.setValue("true");
              rules.add(ruleDef);

              s.setPayload(jm.serialize(trigger));
              Map<String, Object> payload2 = new HashMap<>();
              if (inlineSegment != null)
              {
                payload2.put("inlineSegmentId", respMap.get("inlineSegmentId"));
              }
              s.setPayload2(jm.serialize(payload2));
              s.save(DefinitionItem.State.published);
              respMap.put("id", s.getId());
            }

            respMap.put("name", triggerName);
            resp.getWriter().print(jm.serialize(respMap));
            return;
          }
        }

        App.notifyClearCache(ctx.getNamespace(), SignalPart.PREFIX);
      }
    };

  }

  // /////////////////////////////////////////////////////////////////

  /**
   * @param ctx
   * @param s
   */
  @SuppressWarnings("unchecked")
  private void _deleteTemplateSupportArtifacts(UContext ctx, SignalPart s)
  {
    // Check for existence of payload2 which has references to support
    // artifacts which may need deletion together with this trigger.
    String payload2 = s.getPayload2();
    if (payload2 == null) return;
    Map<String, Object> pl2Map = new JsonMarshaller().readAsMap(payload2);

    if (pl2Map != null && !pl2Map.isEmpty())
    {
      // loops through support artifacts and delete them
      List<Object> artInfos = (List<Object>) pl2Map.get("artifacts");

      if (artInfos != null && !artInfos.isEmpty())
      {
        // disable all the channels (if any) first before delete to prevent
        // it from running while other supportive artifacts had been removed
        TemplateUtils.enableOrDisableAllTemplateChannels(ctx, artInfos, false);

        for (Object artInfo : artInfos)
        {
          Map<String, Object> infoMap = (Map<String, Object>) artInfo;
          if (infoMap == null || infoMap.isEmpty()) continue;

          String subtype = (String) infoMap.get("subtype");
          String configId = (String) infoMap.get("configId");
          boolean deletable = Boolean
              .parseBoolean((String) infoMap.get("deletable"));

          if (subtype == null || configId == null || !deletable) continue;

          DefinitionItem.delete(ctx, ArtifactType.config,
              subtype + "+" + configId);
          App.notifyClearCache(ctx.getNamespace(), subtype);
        }
      }
    }
  }

  /**
   * Gets the references.
   *
   * @param ctx
   *          the ctx
   * @param sid
   *          the sid
   * @return the list
   */
  private List<Object> _getReferences(UContext ctx, String sid)
  {
    String id = sid;

    List<Object> journeyRefs = _getSignalReferences(ctx, Journey.Type.journey,
        id);
    journeyRefs.addAll(_getSignalReferences(ctx, Journey.Type.campaign, id));
    journeyRefs.addAll(_getSignalReferences(ctx, Journey.Type.c1, id));
    return journeyRefs;
  }

  /**
   * Checks if is signal ref found.
   *
   * @param ctx
   *          the ctx
   * @param jType
   *          the j type
   * @param id
   *          the id
   * @return the list
   */
  private List<Object> _getSignalReferences(UContext ctx, Journey.Type jType,
      String id)
  {
    List<Journey> jList = Journey.forceLoadAll(ctx, jType);

    List<Object> journeyRefs = new ArrayList<>();
    List<Journey> jRefs = new ArrayList<>();
    List<StateDef> stateDefs = null;
    List<TimerDef> timerDefs = null;
    List<ActivitySelectorDef> activitySelectorDefs = null;
    boolean isAdded = false;
    for (Journey j : jList)
    {
      if (j == null) continue;
      stateDefs = j.getDef().getStates();
      for (StateDef stateDef : stateDefs)
      {
        isAdded = false;
        timerDefs = stateDef.getTimers();
        activitySelectorDefs = stateDef.getActivities();
        for (TimerDef timerDef : timerDefs)
        {
          if (null != timerDef.getRef() && timerDef.getRef().equals(id))
          {
            jRefs.add(j);
            isAdded = true;
            break;
          }
        }
        if (!isAdded)
        {
          for (ActivitySelectorDef activitySelectorDef : activitySelectorDefs)
          {
            if (null != activitySelectorDef.getRef()
                && activitySelectorDef.getRef().equals(id))
            {
              jRefs.add(j);
              isAdded = true;
              break;
            }
          }
        }
        if (isAdded) break;
      }
    }
    if (null != jRefs && !jRefs.isEmpty())
    {
      _setRefMap(jRefs, journeyRefs, jType);
    }

    return journeyRefs;
  }

  /**
   * Sets the ref map.
   *
   * @param jRefs
   *          the j refs
   * @param journeyRefs
   *          the journey refs
   */
  private void _setRefMap(List<Journey> jRefs, List<Object> journeyRefs,
      Journey.Type jType)
  {

    jRefs.forEach(j -> {
      Map<String, Object> refMap = new HashMap<>();
      refMap.put("id", j.getId());
      refMap.put("name", j.getName());
      refMap.put("type", jType.name());
      journeyRefs.add(refMap);
    });

  }

  /**
   * Retrieves the inline segment ID from the payload2 field of the given
   * SignalPart.
   *
   * @param signalPart
   *          The SignalPart object containing the payload2 field.
   * @return The inlineSegmentId value as a String if found, or null if not.
   */
  private String getInlineSegmentFromPayload2(SignalPart signalPart)
  {
    String inlineSegmentId = null;
    try
    {
      DefinitionItem dItem = signalPart.getDefItem();
      Map<String, Object> payload2 = Const.jsonMarshaller
          .readAsMap(String.valueOf(
              dItem.getValues().get(DefinitionItem.Fields.payload2.name())));
      if (payload2.containsKey("inlineSegmentId"))
      {
        inlineSegmentId = String.valueOf(payload2.get("inlineSegmentId"));
      }
    }
    catch (Exception e)
    {
      // NO OP
    }
    return inlineSegmentId;
  }

}
